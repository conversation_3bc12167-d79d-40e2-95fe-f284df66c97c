<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Army Body Fat Calculator</title>
    <meta name="description" content="This calculator computes body fat percentage using methods from the Army Body Fat Assessment of the Army Body Composition Program.">
    <link rel="stylesheet" href="css/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="images/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/icon-16x16.png">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo">
            <a href="index.html">
                <img src="images/dry-calculator-logo.svg" width="208" height="22" alt="Dry Calculator">
            </a>
        </div>
        <div id="login">
            <!-- Login removed -->
        </div>
    </div>
</div>
<div id="clear"></div>

<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="index.html" itemprop="item">
                    <span itemprop="name">home</span>
                </a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="other-calculators.html" itemprop="item">
                    <span itemprop="name">fitness &amp; health</span>
                </a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="army-body-fat-calculator.html" itemprop="item">
                    <span itemprop="name">army body fat calculator</span>
                </a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        
        <h1>Army Body Fat Calculator</h1>
        <p>The <i>Army Body Fat Calculator</i> is based on the <a href="#reference">Army Body Fat Assessment for the Army Body Composition Program</a>, published on June 12, 2023. To ensure accuracy, take the average of at least three measurements and round to the nearest pound or 0.5 inch.</p>

        <div id="insmdc">
            <img src="images/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use">
        </div>
        
        <form name="calform">
            <div class="panel">
                <table id="calinputtable">
                    <tbody>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="csex1" class="cbcontainer">
                                    <input type="radio" name="csex" id="csex1" value="m" checked="">
                                    <span class="rbmark"></span>male
                                </label> &nbsp;
                                <label for="csex2" class="cbcontainer">
                                    <input type="radio" name="csex" id="csex2" value="f">
                                    <span class="rbmark"></span>female
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <td>Age</td>
                            <td>
                                <input type="text" name="cage" id="cage" value="21" class="infull">
                            </td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td>
                                <input type="text" name="cweight" id="cweight" value="165" class="infull inuipound">
                                <span class="inuipoundspan">pounds</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Waist</td>
                            <td>
                                <table border="0" cellpadding="0" cellspacing="0">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table border="0" cellpadding="0" cellspacing="0">
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <input type="text" name="cwaistfeet" id="cwaistfeet" value="3" class="inhalf inuifoot">
                                                                <span class="inuifootspan">feet</span>
                                                            </td>
                                                            <td>&nbsp;&nbsp;</td>
                                                            <td>
                                                                <input type="text" name="cwaistinch" id="cwaistinch" value="0" class="inhalf inuiinch">
                                                                <span class="inuiinchspan">inches</span>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td style="padding-left:12px;">
                                                abdominal circumference at the level of belly button
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" align="left" style="padding-top: 8px;padding-left: 70px;">
                                <input type="submit" name="x" value="Calculate">
                                <input type="button" value="Clear">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </form>

        <!-- Results Section -->
        <div id="result-section" style="display: none; margin-top: 20px;">
            <div class="h2result">
                <span id="result-title">Body Fat Calculation Results</span>
            </div>
            <div id="results-content" style="background: #f0f8ff; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
                <!-- Results will be populated here -->
            </div>
        </div>

        <br><p></p>
        <fieldset>
            <legend>Related</legend>
            <a href="bmi-calculator.html">BMI Calculator</a> | 
            <a href="calorie-calculator.html">Calorie Calculator</a> | 
            <a href="ideal-weight-calculator.html">Ideal Weight Calculator</a>
        </fieldset>
        <p></p>
        
        <h2 id="reference">Reference</h2>
        <p>Appropriate body fat is one of the medical fitness requirements to join and stay in the U.S. Army. The Department of Defense releases its body fat requirements every few years. In 2021, Army senior leaders conducted a comprehensive study on the relationship between Army Combat Fitness Test (ACFT) scores and Army body-composition standards. The data showed a correlation between body fat percentage and ACFT scores; soldiers with a higher percentage of body fat had lower scores than those with less body fat. Consequently, changes to the Army Body Fat Assessment for the Army Body Composition Program were implemented on June 12, 2023, to increase force readiness by ensuring that all soldiers maintain the necessary level of physical readiness to perform their duties. The provisions of this directive apply to the Regular Army, the Army National Guard/Army National Guard of the United States, and the U.S. Army Reserve. According to this directive, all soldiers who score 540 or higher on the record ACFT, with a minimum of 80 points in each event, are exempt from the body-fat assessment. If not exempted, the body-fat assessment requirements are summarized in the following tables:</p>

        <p style="text-align: center;"><b>Maximum Allowable Percent Body Fat Standards</b>*</p>
        <table class="cinfoT" align="center">
            <tbody>
                <tr>
                    <td class="cinfoHd">Age</td>
                    <td class="cinfoHdL">Male</td>
                    <td class="cinfoHdL">Female</td>
                </tr>
                <tr>
                    <td>17-20</td>
                    <td class="cinfoBodL">20%</td>
                    <td class="cinfoBodL">30%</td>
                </tr>
                <tr>
                    <td>21-27</td>
                    <td class="cinfoBodL">22%</td>
                    <td class="cinfoBodL">32%</td>
                </tr>
                <tr>
                    <td>28-39</td>
                    <td class="cinfoBodL">24%</td>
                    <td class="cinfoBodL">34%</td>
                </tr>
                <tr>
                    <td>40 and over</td>
                    <td class="cinfoBodL">26%</td>
                    <td class="cinfoBodL">36%</td>
                </tr>
            </tbody>
        </table>

        <br>
        <p>According to the Army Body Fat Assessment for the Army Body Composition Program, body fat is calculated using a one-site circumference-based tape method, which requires the measurement of the abdominal circumference at the level of the belly button and body weight. When taking measurements, the subject should be standing upright with arms at rest on both sides. The abdominal circumference should be the average of three measurements and rounded to the nearest 0.5 inch. The body weight should be measured and rounded to the nearest pound. Once measured, you can enter the numbers into the calculator above to find out your body fat percentage and determine if you meet the maximum allowable percent body fat standard.</p>
        
        <p>Soldiers who fail the circumference-based tape method will be flagged, but they may request a supplemental body fat assessment if the means for such testing are reasonably available. These supplemental body fat assessments include:</p>
        <ul>
            <li>Dual X-ray Absorptiometry (DXA)</li>
            <li>InBody 770 Body Composition and Body Water Analyzer</li>
            <li>The Bod Pod Body Composition Tracking System</li>
        </ul>
        <p>Commanders of soldiers who do not request a supplemental body fat assessment or who fail the supplemental body fat assessment will maintain the original flagging action, and the soldier will be enrolled in the Army Body Composition Program (ABCP).</p>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688"
                 crossorigin="anonymous"></script>
            <!-- zishiying -->
            <ins class="adsbygoogle"
                 style="display:block"
                 data-ad-client="ca-pub-2205593928173688"
                 data-ad-slot="8971293106"
                 data-ad-format="auto"
                 data-full-width-responsive="true"></ins>
            <script>
                 (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
        
        <form name="calcSearchForm" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td>
                            <input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators...">
                        </td>
                        <td>
                            <span id="bluebtn">Search</span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div id="calcSearchOut"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="other-calculators.html">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="bmi-calculator.html">BMI</a>
                <a href="calorie-calculator.html">Calorie</a>
                <a href="body-fat-calculator.html">Body Fat</a>
                <a href="bmr-calculator.html">BMR</a>
                <a href="macro-calculator.html">Macro</a>
                <a href="ideal-weight-calculator.html">Ideal Weight</a>
                <a href="pregnancy-calculator.html">Pregnancy</a>
                <a href="pregnancy-weight-gain-calculator.html">Pregnancy Weight Gain</a>
                <a href="pregnancy-conception-calculator.html">Pregnancy Conception</a>
                <a href="due-date-calculator.html">Due Date</a>
                <a href="pace-calculator.html">Pace</a>
                <a href="other-calculators.html">More Fitness and Health Calculators</a>
            </div>
            <div id="ocother">
                <a href="other-calculators.html">Financial</a> | 
                <a href="other-calculators.html">Fitness and Health</a> | 
                <a href="other-calculators.html">Math</a> | 
                <a href="other-calculators.html">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="about-us.html">about us</a> | 
            <a href="sitemap.html">sitemap</a> | 
            <a href="terms.html">terms of use</a> | 
            <a href="privacy.html">privacy policy</a> 
            &nbsp; © 2025 <a href="index.html">drycalculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="other-calculators.html">Financial</a>
    <a href="other-calculators.html" class="topNavOn">Fitness &amp; Health</a>
    <a href="other-calculators.html">Math</a>
    <a href="other-calculators.html">Other</a>
</div>

<script src="js/simple-mobile-scroll.js"></script>
<script src="js/army-body-fat-calculator.js"></script>
</body>
</html>
