// Simple and Reliable Mobile Result Scrolling
// This script provides a foolproof way to scroll to results on mobile devices

class SimpleMobileScroll {
    constructor() {
        this.isMobile = window.innerWidth <= 768;
        this.isScrolling = false;
        this.lastScrollTime = 0;
        
        if (this.isMobile) {
            this.init();
        }
    }
    
    init() {
        console.log('SimpleMobileScroll initialized for mobile device');
        
        // Method 1: Hook into form submissions
        this.hookFormSubmissions();
        
        // Method 2: Monitor result section changes
        this.monitorResultSection();
        
        // Method 3: Provide manual trigger
        this.setupManualTrigger();
        
        // Method 4: Periodic check (fallback)
        this.startPeriodicCheck();
    }
    
    hookFormSubmissions() {
        // Find all calculator forms
        const forms = document.querySelectorAll('form[name="calform"]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                console.log('Form submitted, waiting for results...');
                
                // Wait for results to appear
                this.waitForResults();
            });
        });
        
        // Also hook into any calculate buttons
        const calculateButtons = document.querySelectorAll('input[type="submit"][value*="alculate"], input[type="submit"][name="x"]');
        calculateButtons.forEach(button => {
            button.addEventListener('click', () => {
                console.log('Calculate button clicked');
                setTimeout(() => this.waitForResults(), 100);
            });
        });
    }
    
    monitorResultSection() {
        const resultSection = document.getElementById('result-section');
        if (!resultSection) {
            console.log('No result section found');
            return;
        }
        
        // Use MutationObserver to watch for changes
        const observer = new MutationObserver((mutations) => {
            let shouldScroll = false;
            
            mutations.forEach((mutation) => {
                // Check for style changes
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const target = mutation.target;
                    if (target.id === 'result-section') {
                        const isVisible = this.isElementVisible(target);
                        if (isVisible && !this.isScrolling) {
                            shouldScroll = true;
                        }
                    }
                }
                
                // Check for content changes
                if (mutation.type === 'childList') {
                    const target = mutation.target;
                    if (target.id === 'results-content' || target.closest('#result-section')) {
                        const resultSection = document.getElementById('result-section');
                        if (resultSection && this.isElementVisible(resultSection) && !this.isScrolling) {
                            shouldScroll = true;
                        }
                    }
                }
            });
            
            if (shouldScroll) {
                console.log('MutationObserver detected result changes');
                setTimeout(() => this.scrollToResults(), 200);
            }
        });
        
        // Start observing
        observer.observe(resultSection, {
            attributes: true,
            attributeFilter: ['style'],
            childList: true,
            subtree: true
        });
        
        // Also observe the results content
        const resultsContent = document.getElementById('results-content');
        if (resultsContent) {
            observer.observe(resultsContent, {
                childList: true,
                subtree: true
            });
        }
    }
    
    setupManualTrigger() {
        // Global function for manual triggering
        window.scrollToMobileResults = () => {
            console.log('Manual scroll trigger called');
            this.scrollToResults();
        };
        
        // Also add to window object for easy access
        window.simpleMobileScroll = this;
    }
    
    startPeriodicCheck() {
        // Check every 2 seconds for new results
        setInterval(() => {
            const resultSection = document.getElementById('result-section');
            if (resultSection && 
                this.isElementVisible(resultSection) && 
                !this.isScrolling &&
                !resultSection.dataset.autoScrolled) {
                
                console.log('Periodic check found new results');
                this.scrollToResults();
            }
        }, 2000);
    }
    
    waitForResults() {
        let attempts = 0;
        const maxAttempts = 15; // 3 seconds
        
        const checkResults = () => {
            attempts++;
            const resultSection = document.getElementById('result-section');
            
            if (resultSection && this.isElementVisible(resultSection)) {
                console.log(`Results appeared after ${attempts} attempts`);
                setTimeout(() => this.scrollToResults(), 100);
                return;
            }
            
            if (attempts < maxAttempts) {
                setTimeout(checkResults, 200);
            } else {
                console.log('Timeout waiting for results');
            }
        };
        
        // Start checking after a short delay
        setTimeout(checkResults, 300);
    }
    
    isElementVisible(element) {
        if (!element) return false;
        
        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               element.offsetHeight > 0 && 
               element.offsetWidth > 0 &&
               rect.height > 0;
    }
    
    scrollToResults() {
        if (!this.isMobile || this.isScrolling) {
            return;
        }
        
        const now = Date.now();
        if (now - this.lastScrollTime < 1000) {
            console.log('Scroll throttled');
            return;
        }
        
        const resultSection = document.getElementById('result-section');
        if (!resultSection || !this.isElementVisible(resultSection)) {
            console.log('Cannot scroll: result section not visible');
            return;
        }
        
        this.isScrolling = true;
        this.lastScrollTime = now;
        
        console.log('Starting scroll to results...');
        
        // Mark as auto-scrolled to prevent duplicate scrolls
        resultSection.dataset.autoScrolled = 'true';
        
        // Calculate scroll position
        const headerHeight = this.getHeaderHeight();
        const offset = 20;
        const targetY = resultSection.offsetTop - headerHeight - offset;
        const finalY = Math.max(0, targetY);
        
        console.log(`Scrolling from ${window.scrollY} to ${finalY}`);
        
        // Perform the scroll
        this.performScroll(finalY).then(() => {
            console.log('Scroll completed');
            this.highlightResults();
            
            // Reset scrolling flag
            setTimeout(() => {
                this.isScrolling = false;
                // Reset auto-scrolled flag after some time
                setTimeout(() => {
                    delete resultSection.dataset.autoScrolled;
                }, 5000);
            }, 500);
        });
    }
    
    getHeaderHeight() {
        const header = document.getElementById('header');
        const headerOut = document.getElementById('headerout');
        
        let height = 0;
        if (header) height += header.offsetHeight;
        if (headerOut && headerOut !== header) height += headerOut.offsetHeight;
        
        return height || 60; // Fallback
    }
    
    performScroll(targetY) {
        return new Promise((resolve) => {
            // Try smooth scroll first
            try {
                window.scrollTo({
                    top: targetY,
                    behavior: 'smooth'
                });
                
                // Check if scroll completed
                const checkScroll = () => {
                    const currentY = window.scrollY;
                    const diff = Math.abs(currentY - targetY);
                    
                    if (diff < 10) {
                        resolve();
                    } else {
                        setTimeout(checkScroll, 100);
                    }
                };
                
                setTimeout(checkScroll, 100);
                
            } catch (e) {
                // Fallback to instant scroll
                console.log('Smooth scroll failed, using instant scroll');
                window.scrollTo(0, targetY);
                resolve();
            }
            
            // Timeout fallback
            setTimeout(resolve, 1000);
        });
    }
    
    highlightResults() {
        const resultSection = document.getElementById('result-section');
        if (!resultSection) return;
        
        console.log('Highlighting results');
        
        // Add highlight effect
        resultSection.style.transition = 'all 0.3s ease';
        resultSection.style.boxShadow = '0 4px 20px rgba(0, 123, 255, 0.4)';
        resultSection.style.borderLeft = '4px solid #007bff';
        
        // Add success message
        this.showSuccessMessage();
        
        // Vibrate if supported
        if ('vibrate' in navigator) {
            navigator.vibrate([100, 50, 100]);
        }
        
        // Remove highlight
        setTimeout(() => {
            resultSection.style.boxShadow = '';
            resultSection.style.borderLeft = '';
        }, 2000);
    }
    
    showSuccessMessage() {
        // Remove existing message
        const existing = document.querySelector('.mobile-scroll-success');
        if (existing) existing.remove();
        
        // Create success message
        const message = document.createElement('div');
        message.className = 'mobile-scroll-success';
        message.innerHTML = '📊 Results Ready';
        message.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            animation: slideInOut 2s ease;
        `;
        
        document.body.appendChild(message);
        
        // Add animation
        if (!document.querySelector('#mobile-scroll-animation')) {
            const style = document.createElement('style');
            style.id = 'mobile-scroll-animation';
            style.textContent = `
                @keyframes slideInOut {
                    0% { transform: translateX(100%); opacity: 0; }
                    15% { transform: translateX(0); opacity: 1; }
                    85% { transform: translateX(0); opacity: 1; }
                    100% { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Remove message
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 2000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.simpleMobileScroll = new SimpleMobileScroll();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading
} else {
    // DOM is already loaded
    window.simpleMobileScroll = new SimpleMobileScroll();
}
