// Mobile Optimization Module for Dry Calculator
class MobileOptimization {
    constructor() {
        this.isMobile = this.detectMobile();
        this.isTablet = this.detectTablet();
        this.touchDevice = this.detectTouch();
        
        this.init();
    }
    
    detectMobile() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    detectTablet() {
        return window.innerWidth > 768 && window.innerWidth <= 1024;
    }
    
    detectTouch() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    
    init() {
        this.setupResultScrolling();
        this.setupFormOptimizations();
        this.setupTouchOptimizations();
        this.setupKeyboardOptimizations();
        this.setupOrientationHandling();
        
        // Add mobile-specific event listeners
        if (this.isMobile) {
            this.addMobileEventListeners();
        }
    }
    
    setupResultScrolling() {
        // Multiple approaches to detect result display
        this.setupMutationObserver();
        this.setupFormSubmissionHook();
        this.setupManualTriggers();
    }

    setupMutationObserver() {
        // Monitor for result section visibility changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // Check for style changes (display property)
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const target = mutation.target;
                    if (target.id === 'result-section') {
                        const isVisible = target.style.display !== 'none' &&
                                        target.style.display !== '' &&
                                        target.offsetHeight > 0;
                        if (isVisible) {
                            console.log('Result section became visible via style change');
                            this.scrollToResults();
                        }
                    }
                }

                // Check for content changes in results
                if (mutation.type === 'childList') {
                    const target = mutation.target;
                    if (target.id === 'results-content' || target.id === 'result-section') {
                        const resultSection = document.getElementById('result-section');
                        if (resultSection && resultSection.style.display !== 'none' && resultSection.offsetHeight > 0) {
                            console.log('Result content changed');
                            setTimeout(() => this.scrollToResults(), 200);
                        }
                    }
                }
            });
        });

        // Start observing
        const resultSection = document.getElementById('result-section');
        if (resultSection) {
            observer.observe(resultSection, {
                attributes: true,
                attributeFilter: ['style', 'class'],
                childList: true,
                subtree: true
            });
        }

        // Also monitor results-content directly
        const resultsContent = document.getElementById('results-content');
        if (resultsContent) {
            observer.observe(resultsContent, {
                childList: true,
                subtree: true
            });
        }

        // Monitor document body for any result-related changes
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    setupFormSubmissionHook() {
        // Hook into form submissions to trigger scroll after calculation
        const forms = document.querySelectorAll('form[name="calform"]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                console.log('Form submitted, setting up result detection');
                // Set up a delayed check for results
                this.waitForResults();
            });
        });
    }

    setupManualTriggers() {
        // Add global function for manual triggering
        window.triggerMobileResultScroll = () => {
            console.log('Manual trigger for result scroll');
            this.scrollToResults();
        };

        // Check periodically for results (fallback)
        if (this.isMobile) {
            this.startPeriodicCheck();
        }
    }

    waitForResults() {
        let attempts = 0;
        const maxAttempts = 20; // 4 seconds total

        const checkForResults = () => {
            attempts++;
            const resultSection = document.getElementById('result-section');

            if (resultSection && resultSection.style.display !== 'none' && resultSection.offsetHeight > 0) {
                console.log(`Results found after ${attempts} attempts`);
                setTimeout(() => this.scrollToResults(), 100);
                return;
            }

            if (attempts < maxAttempts) {
                setTimeout(checkForResults, 200);
            } else {
                console.log('Result detection timeout');
            }
        };

        setTimeout(checkForResults, 300); // Start checking after 300ms
    }

    startPeriodicCheck() {
        // Periodic check as ultimate fallback
        setInterval(() => {
            const resultSection = document.getElementById('result-section');
            if (resultSection &&
                resultSection.style.display !== 'none' &&
                resultSection.offsetHeight > 0 &&
                !resultSection.dataset.scrolled) {

                console.log('Periodic check found results');
                resultSection.dataset.scrolled = 'true';
                this.scrollToResults();

                // Reset flag after some time
                setTimeout(() => {
                    delete resultSection.dataset.scrolled;
                }, 5000);
            }
        }, 1000);
    }
    
    scrollToResults() {
        if (!this.isMobile) {
            console.log('Not mobile device, skipping scroll');
            return;
        }

        const resultSection = document.getElementById('result-section');
        if (!resultSection) {
            console.log('Result section not found');
            return;
        }

        // Check if results are actually visible
        const isDisplayed = resultSection.style.display !== 'none' &&
                           resultSection.offsetHeight > 0 &&
                           resultSection.offsetWidth > 0;

        if (!isDisplayed) {
            console.log('Result section not displayed');
            return;
        }

        console.log('Scrolling to results...');

        // Add a delay to ensure content is fully rendered
        setTimeout(() => {
            // Force a layout recalculation
            resultSection.offsetHeight;

            const rect = resultSection.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const scrollY = window.scrollY;

            // Check if results are already in view
            const isInView = rect.top >= 0 && rect.top <= viewportHeight * 0.7;

            console.log(`Result position: top=${rect.top}, bottom=${rect.bottom}, viewport=${viewportHeight}, inView=${isInView}`);

            if (!isInView) {
                // Calculate optimal scroll position
                const headerHeight = this.getHeaderHeight();
                const offset = 20; // Additional padding
                const targetPosition = resultSection.offsetTop - headerHeight - offset;

                console.log(`Scrolling to position: ${targetPosition}`);

                // Use multiple scroll methods for better compatibility
                this.performScroll(targetPosition);

                // Add visual feedback
                setTimeout(() => this.highlightResults(), 300);
            } else {
                console.log('Results already in view');
                // Still add highlight for feedback
                this.highlightResults();
            }
        }, 250);
    }

    getHeaderHeight() {
        const header = document.getElementById('header');
        const headerOut = document.getElementById('headerout');

        let headerHeight = 0;
        if (header) headerHeight += header.offsetHeight;
        if (headerOut) headerHeight += headerOut.offsetHeight;

        // Fallback height
        if (headerHeight === 0) headerHeight = 60;

        return headerHeight;
    }

    performScroll(targetPosition) {
        const finalPosition = Math.max(0, targetPosition);

        // Method 1: Modern smooth scroll
        try {
            window.scrollTo({
                top: finalPosition,
                behavior: 'smooth'
            });
        } catch (e) {
            console.log('Smooth scroll failed, using fallback');
            // Method 2: Fallback for older browsers
            this.animateScroll(finalPosition);
        }

        // Method 3: Force scroll as backup
        setTimeout(() => {
            if (Math.abs(window.scrollY - finalPosition) > 50) {
                console.log('Forcing scroll position');
                window.scrollTo(0, finalPosition);
            }
        }, 1000);
    }

    animateScroll(targetPosition) {
        const startPosition = window.scrollY;
        const distance = targetPosition - startPosition;
        const duration = 500;
        let startTime = null;

        const animation = (currentTime) => {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);

            // Easing function
            const ease = progress * (2 - progress);
            const currentPosition = startPosition + (distance * ease);

            window.scrollTo(0, currentPosition);

            if (progress < 1) {
                requestAnimationFrame(animation);
            }
        };

        requestAnimationFrame(animation);
    }
    
    highlightResults() {
        const resultSection = document.getElementById('result-section');
        if (!resultSection) {
            console.log('Cannot highlight: result section not found');
            return;
        }

        console.log('Highlighting results section');

        // Add highlight animation
        resultSection.style.transition = 'all 0.3s ease';
        resultSection.style.boxShadow = '0 4px 20px rgba(0, 123, 255, 0.3)';
        resultSection.style.transform = 'scale(1.01)';
        resultSection.style.borderColor = '#007bff';

        // Add success indicator
        this.showSuccessIndicator(resultSection);

        // Vibrate if supported
        if ('vibrate' in navigator) {
            navigator.vibrate(100);
        }

        // Remove highlight after animation
        setTimeout(() => {
            resultSection.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            resultSection.style.transform = 'scale(1)';
            resultSection.style.borderColor = '';
        }, 2000);
    }

    showSuccessIndicator(resultSection) {
        // Remove any existing indicator
        const existingIndicator = resultSection.querySelector('.mobile-success-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Create success indicator
        const indicator = document.createElement('div');
        indicator.className = 'mobile-success-indicator';
        indicator.innerHTML = '✓ Results Ready';
        indicator.style.cssText = `
            position: absolute;
            top: -15px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 1000;
            animation: slideInFade 2s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        `;

        // Ensure result section has relative positioning
        resultSection.style.position = 'relative';
        resultSection.appendChild(indicator);

        // Add animation keyframes if not exists
        if (!document.querySelector('#mobile-success-animation')) {
            const style = document.createElement('style');
            style.id = 'mobile-success-animation';
            style.textContent = `
                @keyframes slideInFade {
                    0% {
                        opacity: 0;
                        transform: translateY(-10px) scale(0.8);
                    }
                    20% {
                        opacity: 1;
                        transform: translateY(0) scale(1.1);
                    }
                    80% {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                    100% {
                        opacity: 0;
                        transform: translateY(-5px) scale(0.9);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // Remove indicator after animation
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 2000);
    }
    
    setupFormOptimizations() {
        if (!this.isMobile) return;
        
        // Optimize input fields for mobile
        const inputs = document.querySelectorAll('input[type="text"], input[type="number"], select');
        inputs.forEach(input => {
            // Prevent zoom on focus for iOS
            if (parseFloat(getComputedStyle(input).fontSize) < 16) {
                input.style.fontSize = '16px';
            }
            
            // Add mobile-friendly attributes
            if (input.type === 'text' && input.name.includes('temp')) {
                input.setAttribute('inputmode', 'decimal');
                input.setAttribute('pattern', '[0-9]*');
            }
            
            // Optimize for touch
            input.style.minHeight = '44px';
            input.style.padding = '12px';
        });
        
        // Optimize buttons
        const buttons = document.querySelectorAll('input[type="submit"], input[type="button"], button');
        buttons.forEach(button => {
            button.style.minHeight = '44px';
            button.style.minWidth = '44px';
            button.style.padding = '12px 20px';
        });
    }
    
    setupTouchOptimizations() {
        if (!this.touchDevice) return;
        
        // Add touch feedback to interactive elements
        const interactiveElements = document.querySelectorAll('button, input[type="submit"], input[type="button"], .scifunc, .scinm, .sciop, .scieq');
        
        interactiveElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.95)';
                element.style.transition = 'transform 0.1s ease';
            });
            
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 100);
            });
            
            element.addEventListener('touchcancel', (e) => {
                element.style.transform = 'scale(1)';
            });
        });
    }
    
    setupKeyboardOptimizations() {
        if (!this.isMobile) return;
        
        // Handle virtual keyboard appearance
        let initialViewportHeight = window.innerHeight;
        
        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;
            
            // If height decreased significantly, keyboard is likely open
            if (heightDifference > 150) {
                document.body.classList.add('keyboard-open');
                
                // Ensure focused input is visible
                const activeElement = document.activeElement;
                if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'SELECT')) {
                    setTimeout(() => {
                        activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }, 300);
                }
            } else {
                document.body.classList.remove('keyboard-open');
            }
        });
        
        // Handle input focus
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                setTimeout(() => {
                    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });
        });
    }
    
    setupOrientationHandling() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                // Recalculate layout after orientation change
                this.recalculateLayout();
            }, 500);
        });
    }
    
    recalculateLayout() {
        // Force layout recalculation
        const resultSection = document.getElementById('result-section');
        if (resultSection && resultSection.style.display !== 'none') {
            // Re-scroll to results if they were visible
            setTimeout(() => this.scrollToResults(), 100);
        }
    }
    
    addMobileEventListeners() {
        // Add swipe gestures for navigation (optional)
        let startX, startY;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchmove', (e) => {
            // Prevent pull-to-refresh on some browsers
            if (window.scrollY === 0 && e.touches[0].clientY > startY) {
                e.preventDefault();
            }
        }, { passive: false });
        
        // Add loading states for better UX
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('input[type="submit"]');
                if (submitBtn) {
                    submitBtn.style.opacity = '0.7';
                    submitBtn.value = 'Calculating...';
                    
                    setTimeout(() => {
                        submitBtn.style.opacity = '1';
                        submitBtn.value = 'Calculate';
                    }, 500);
                }
            });
        });
    }
    
    // Public method to manually trigger result scrolling
    static scrollToResults() {
        const instance = window.mobileOptimization;
        if (instance) {
            instance.scrollToResults();
        }
    }
    
    // Public method to check if device is mobile
    static isMobileDevice() {
        const instance = window.mobileOptimization;
        return instance ? instance.isMobile : false;
    }
}

// CSS for mobile optimizations
const mobileCSS = `
<style>
/* Mobile-specific optimizations */
@media (max-width: 768px) {
    /* Keyboard open state */
    body.keyboard-open {
        position: fixed;
        width: 100%;
    }
    
    /* Result section mobile enhancements */
    #result-section {
        margin-top: 20px !important;
        padding: 15px !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        transition: all 0.3s ease !important;
    }
    
    /* Touch feedback */
    .touch-feedback {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
    
    /* Loading state */
    .loading {
        opacity: 0.7;
        pointer-events: none;
    }
    
    /* Improved form spacing */
    #calinputtable tr {
        margin-bottom: 15px !important;
    }
    
    /* Better button spacing */
    input[type="submit"], input[type="button"] {
        margin: 8px 0 !important;
    }
    
    /* Scroll indicator for results */
    #result-section::before {
        content: "📊 Results";
        display: block;
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
        text-align: center;
    }
}

/* Smooth scrolling for all devices */
html {
    scroll-behavior: smooth;
}

/* Focus improvements */
input:focus, select:focus, button:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}
</style>
`;

// Inject CSS
document.head.insertAdjacentHTML('beforeend', mobileCSS);

// Initialize mobile optimization when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mobileOptimization = new MobileOptimization();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileOptimization;
}
