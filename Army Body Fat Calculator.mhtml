From: <Saved by Blink>
Snapshot-Content-Location: https://www.calculator.net/army-body-fat-calculator.html
Subject: Army Body Fat Calculator
Date: Tue, 17 Jun 2025 16:55:05 -0700
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ----"


------MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.calculator.net/army-body-fat-calculator.html

<!DOCTYPE html><html lang=3D"en"><head><meta http-equiv=3D"Content-Type" co=
ntent=3D"text/html; charset=3DUTF-8">
	<title>Army Body Fat Calculator</title>
	<meta name=3D"description" content=3D"This calculator computes body fat pe=
rcentage using methods from the Army Body Fat Assessment of the Army Body C=
omposition Program.">
	<link rel=3D"stylesheet" href=3D"https://www.calculator.net/style.css"><me=
ta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=3D1.0">=
	<link rel=3D"apple-touch-icon" sizes=3D"180x180" href=3D"https://d26tpo4cm=
8sb6k.cloudfront.net/apple-touch-icon.png">
	<link rel=3D"icon" type=3D"image/png" sizes=3D"32x32" href=3D"https://d26t=
po4cm8sb6k.cloudfront.net/icon-32x32.png">
	<link rel=3D"icon" type=3D"image/png" sizes=3D"16x16" href=3D"https://d26t=
po4cm8sb6k.cloudfront.net/icon-16x16.png">
	<link rel=3D"manifest" href=3D"https://www.calculator.net/manifest.json"><=
meta http-equiv=3D"origin-trial" content=3D"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6L=
YjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOi=
JodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0Z=
WRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVl=
fQ=3D=3D"><meta http-equiv=3D"origin-trial" content=3D"Amm8/NmvvQfhwCib6I7Z=
smUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJ=
vcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2=
ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU=
3ViZG9tYWluIjp0cnVlfQ=3D=3D"><meta http-equiv=3D"origin-trial" content=3D"A=
9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO=
8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF=
0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MD=
AsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv=3D=
"origin-trial" content=3D"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkC=
QeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2ds=
ZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25=
TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUG=
FydHkiOnRydWV9"><link href=3D"https://securepubads.g.doubleclick.net/pagead=
/managed/dict/m202506170101/gpt" rel=3D"compression-dictionary"></head>
<body>
<div id=3D"headerout">
	<div id=3D"header">
		<div id=3D"logo"><a href=3D"https://www.calculator.net/"><img src=3D"http=
s://d26tpo4cm8sb6k.cloudfront.net/img/svg/calculator-white.svg" width=3D"20=
8" height=3D"22" alt=3D"Calculator.net"></a></div>
		<div id=3D"login"><a href=3D"https://www.calculator.net/my-account/sign-i=
n.php">sign in</a></div>	</div>
</div>
<div id=3D"clear"></div><div id=3D"contentout"><div id=3D"content"><div id=
=3D"breadcrumbs" itemscope=3D"" itemtype=3D"https://schema.org/BreadcrumbLi=
st"><span itemprop=3D"itemListElement" itemscope=3D"" itemtype=3D"https://s=
chema.org/ListItem"><a href=3D"https://www.calculator.net/" itemprop=3D"ite=
m"><span itemprop=3D"name">home</span></a><meta itemprop=3D"position" conte=
nt=3D"1"></span> / <span itemprop=3D"itemListElement" itemscope=3D"" itemty=
pe=3D"https://schema.org/ListItem"><a href=3D"https://www.calculator.net/fi=
tness-and-health-calculator.html" itemprop=3D"item"><span itemprop=3D"name"=
>fitness &amp; health</span></a><meta itemprop=3D"position" content=3D"2"><=
/span> / <span itemprop=3D"itemListElement" itemscope=3D"" itemtype=3D"http=
s://schema.org/ListItem"><a href=3D"https://www.calculator.net/army-body-fa=
t-calculator.html" itemprop=3D"item"><span itemprop=3D"name">army body fat =
calculator</span></a><meta itemprop=3D"position" content=3D"3"></span></div=
>		<h1>Army Body Fat Calculator</h1>
<p>The <i>Army Body Fat Calculator</i> is based on the <a href=3D"https://w=
ww.calculator.net/pdf/alaract-046-2023.pdf">Army Body Fat Assessment for th=
e Army Body Composition Program</a>, published on June 12, 2023. To ensure =
accuracy, take the average of at least three measurements and round to the =
nearest pound or 0.5 inch.</p>

<div id=3D"insmdc"><img src=3D"https://d26tpo4cm8sb6k.cloudfront.net/img/sv=
g/insm.svg" width=3D"630" height=3D"35" alt=3D"Modify the values and click =
the calculate button to use"></div><form name=3D"calform">
<div class=3D"panel">
<table id=3D"calinputtable">
<tbody><tr>
	<td>Gender</td>
	<td>
		<label for=3D"csex1" class=3D"cbcontainer"><input type=3D"radio" name=3D"=
csex" id=3D"csex1" value=3D"m" checked=3D""><span class=3D"rbmark"></span>m=
ale</label> &nbsp;
		<label for=3D"csex2" class=3D"cbcontainer"><input type=3D"radio" name=3D"=
csex" id=3D"csex2" value=3D"f"><span class=3D"rbmark"></span>female</label>
	</td>
</tr>
<tr>
	<td>Age</td>
	<td><input type=3D"text" name=3D"cage" id=3D"cage" value=3D"21" class=3D"i=
nfull"></td>
</tr>
<tr>
	<td>Weight</td>
	<td><input type=3D"text" name=3D"cweight" id=3D"cweight" value=3D"165" cla=
ss=3D"infull inuipound"><span class=3D"inuipoundspan">pounds</span></td>
</tr>
<tr>
	<td>Waist</td>
	<td>
		<table border=3D"0" cellpadding=3D"0" cellspacing=3D"0"><tbody><tr><td><t=
able border=3D"0" cellpadding=3D"0" cellspacing=3D"0"><tbody><tr><td>
			<input type=3D"text" name=3D"cwaistfeet" id=3D"cwaistfeet" value=3D"3" c=
lass=3D"inhalf inuifoot"><span class=3D"inuifootspan">feet</span>
		</td><td>&nbsp;&nbsp;</td><td>
			<input type=3D"text" name=3D"cwaistinch" id=3D"cwaistinch" value=3D"0" c=
lass=3D"inhalf inuiinch"><span class=3D"inuiinchspan">inches</span>
		</td></tr></tbody></table></td><td style=3D"padding-left:12px;">abdominal=
 circumference at the level of belly button</td></tr></tbody></table>
	</td>
</tr>
<tr>
	<td colspan=3D"2" align=3D"left" style=3D"padding-top: 8px;padding-left: 7=
0px;">
		<input type=3D"submit" name=3D"x" value=3D"Calculate">
		<input type=3D"button" value=3D"Clear">
	</td>
</tr>
</tbody></table>
</div>
</form>

<br><p></p><fieldset><legend>Related</legend><a href=3D"https://www.calcula=
tor.net/bmi-calculator.html">BMI Calculator</a> | <a href=3D"https://www.ca=
lculator.net/calorie-calculator.html">Calorie Calculator</a> | <a href=3D"h=
ttps://www.calculator.net/ideal-weight-calculator.html">Ideal Weight Calcul=
ator</a></fieldset><p></p>
<h2>Reference</h2>
<p>Appropriate body fat is one of the medical fitness requirements to join =
and stay in the U.S. Army. The Department of Defense releases its body fat =
requirements every few years. In 2021, Army senior leaders conducted a comp=
rehensive study on the relationship between Army Combat Fitness Test (ACFT)=
 scores and Army body-composition standards. The data showed a correlation =
between body fat percentage and ACFT scores; soldiers with a higher percent=
age of body fat had lower scores than those with less body fat. Consequentl=
y, changes to the Army Body Fat Assessment for the Army Body Composition Pr=
ogram were implemented on June 12, 2023, to increase force readiness by ens=
uring that all soldiers maintain the necessary level of physical readiness =
to perform their duties. The provisions of this directive apply to the Regu=
lar Army, the Army National Guard/Army National Guard of the United States,=
 and the U.S. Army Reserve. According to this directive, all soldiers who s=
core 540 or higher on the record ACFT, with a minimum of 80 points in each =
event, are exempt from the body-fat assessment. If not exempted, the body-f=
at assessment requirements are summarized in the following tables:</p>

<p style=3D"text-align: center;"><b>Maximum Allowable Percent Body Fat Stan=
dards</b>*</p>
<table class=3D"cinfoT" align=3D"center">
	<tbody><tr><td class=3D"cinfoHd">Age</td><td class=3D"cinfoHdL">Male</td><=
td class=3D"cinfoHdL">Female</td></tr>
	<tr><td>17-20</td><td class=3D"cinfoBodL">20%</td><td class=3D"cinfoBodL">=
30%</td></tr>
	<tr><td>21-27</td><td class=3D"cinfoBodL">22%</td><td class=3D"cinfoBodL">=
32%</td></tr>
	<tr><td>28-39</td><td class=3D"cinfoBodL">24%</td><td class=3D"cinfoBodL">=
34%</td></tr>
	<tr><td>40 and over</td><td class=3D"cinfoBodL">26%</td><td class=3D"cinfo=
BodL">36%</td></tr>
</tbody></table>

<br><p>According to the Army Body Fat Assessment for the Army Body Composit=
ion Program, body fat is calculated using a one-site circumference-based ta=
pe method, which requires the measurement of the abdominal circumference at=
 the level of the belly button and body weight. When taking measurements, t=
he subject should be standing upright with arms at rest on both sides. The =
abdominal circumference should be the average of three measurements and rou=
nded to the nearest 0.5 inch. The body weight should be measured and rounde=
d to the nearest pound. Once measured, you can enter the numbers into the c=
alculator above to find out your body fat percentage and determine if you m=
eet the maximum allowable percent body fat standard.</p>
<p>Soldiers who fail the circumference-based tape method will be flagged, b=
ut they may request a supplemental body fat assessment if the means for suc=
h testing are reasonably available. These supplemental body fat assessments=
 include:</p>
<ul>
	<li>Dual X-ray Absorptiometry (DXA)</li>
	<li>InBody 770 Body Composition and Body Water Analyzer</li>
	<li>The Bod Pod Body Composition Tracking System</li>
</ul>
<p>Commanders of soldiers who do not request a supplemental body fat assess=
ment or who fail the supplemental body fat assessment will maintain the ori=
ginal flagging action, and the soldier will be enrolled in the Army Body Co=
mposition Program (ABCP).</p>






</div>
	<div id=3D"right">
		<div style=3D"padding-top:10px;  min-height:280px; text-align:center;">


<!-- /1057446/Calculator-Top-Right-Desktop -->
<div id=3D"div-gpt-ad-1589567013929-0" style=3D"" data-google-query-id=3D""=
>
  <div id=3D"google_ads_iframe_/1057446/Calculator-Top-Right-Desktop_0__con=
tainer__" style=3D"border: 0pt none; width: 300px; height: 0px;"></div>
</div>
</div>
<form name=3D"calcSearchForm" autocomplete=3D"off"><table align=3D"center" =
id=3D"searchbox"><tbody><tr><td><input type=3D"text" name=3D"calcSearchTerm=
" id=3D"calcSearchTerm" class=3D"inlongest"></td><td><span id=3D"bluebtn">S=
earch</span></td></tr><tr><td colspan=3D"2"><div id=3D"calcSearchOut"></div=
></td></tr></tbody></table></form><div id=3D"othercalc"><div id=3D"octitle"=
><a href=3D"https://www.calculator.net/fitness-and-health-calculator.html">=
Fitness and Health Calculators</a></div><div id=3D"occontent"><a href=3D"ht=
tps://www.calculator.net/bmi-calculator.html">BMI</a><a href=3D"https://www=
.calculator.net/calorie-calculator.html">Calorie</a><a href=3D"https://www.=
calculator.net/body-fat-calculator.html">Body Fat</a><a href=3D"https://www=
.calculator.net/bmr-calculator.html">BMR</a><a href=3D"https://www.calculat=
or.net/macro-calculator.html">Macro</a><a href=3D"https://www.calculator.ne=
t/ideal-weight-calculator.html">Ideal Weight</a><a href=3D"https://www.calc=
ulator.net/pregnancy-calculator.html">Pregnancy</a><a href=3D"https://www.c=
alculator.net/pregnancy-weight-gain-calculator.html">Pregnancy Weight Gain<=
/a><a href=3D"https://www.calculator.net/pregnancy-conception-calculator.ht=
ml">Pregnancy Conception</a><a href=3D"https://www.calculator.net/due-date-=
calculator.html">Due Date</a><a href=3D"https://www.calculator.net/pace-cal=
culator.html">Pace</a><a href=3D"https://www.calculator.net/fitness-and-hea=
lth-calculator.html">More Fitness and Health Calculators</a></div><div id=
=3D"ocother"><a href=3D"https://www.calculator.net/financial-calculator.htm=
l">Financial</a> | <a href=3D"https://www.calculator.net/fitness-and-health=
-calculator.html">Fitness and Health</a> | <a href=3D"https://www.calculato=
r.net/math-calculator.html">Math</a> | <a href=3D"https://www.calculator.ne=
t/other-calculator.html">Other</a></div></div>	</div>
</div>
<div id=3D"clear"></div>
<div id=3D"footer"><div id=3D"footerin"><div id=3D"footernav"><a href=3D"ht=
tps://www.calculator.net/about-us.html">about us</a> | <a href=3D"https://w=
ww.calculator.net/sitemap.html">sitemap</a> | <a href=3D"https://www.calcul=
ator.net/about-us.html#terms">terms of use</a> | <a href=3D"https://www.cal=
culator.net/about-us.html#privacy">privacy policy</a> &nbsp;  =C2=A9 2008 -=
 2025 <a href=3D"https://www.calculator.net/">calculator.net</a></div></div=
></div>
<div class=3D"topNavAbs">
<a href=3D"https://www.calculator.net/financial-calculator.html">Financial<=
/a>
<a href=3D"https://www.calculator.net/fitness-and-health-calculator.html" c=
lass=3D"topNavOn">Fitness &amp; Health</a>
<a href=3D"https://www.calculator.net/math-calculator.html">Math</a>
<a href=3D"https://www.calculator.net/other-calculator.html">Other</a>
</div>


<iframe src=3D"cid:<EMAIL>" widt=
h=3D"0" height=3D"0" style=3D"display: none;"></iframe></body><iframe name=
=3D"goog_topics_frame" src=3D"cid:frame-10F3A907DC7F30DC090C8927108C7521@mh=
tml.blink" style=3D"display: none;"></iframe></html>
------MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ----
Content-Type: image/svg+xml
Content-Transfer-Encoding: quoted-printable
Content-Location: https://d26tpo4cm8sb6k.cloudfront.net/img/svg/insm.svg

<?xml version=3D"1.0" encoding=3D"UTF-8"?>
<svg width=3D"630" height=3D"35" viewBox=3D"0 0 630 35" xmlns=3D"http://www=
.w3.org/2000/svg"><rect x=3D"0" y=3D"0" width=3D"630" height=3D"35" fill=3D=
"#369" /><text x=3D"136" y=3D"25" fill=3D"#fff" font-size=3D"17px" font-fam=
ily=3D"Arial">Modify the values and click the Calculate button to use</text=
><circle cx=3D"71" cy=3D"18" r=3D"13" fill=3D"#fff"/><path d=3D"M62 15 L80 =
15 L71 26Z" fill=3D"#369"/></svg>
------MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ----
Content-Type: image/svg+xml
Content-Transfer-Encoding: quoted-printable
Content-Location: https://d26tpo4cm8sb6k.cloudfront.net/img/svg/calculator-white.svg

<?xml version=3D"1.0" encoding=3D"UTF-8"?>
<svg width=3D"208" height=3D"22" viewBox=3D"0 0 208 22" xmlns=3D"http://www=
.w3.org/2000/svg"><defs><style>.cls-1{fill:#ffffff;}.cls-2{fill:#ccff99;}</=
style></defs><title>Calculator.net</title><g transform=3D"matrix(2.0833 0 0=
 2.082 -2.3542 -19.565)"><path class=3D"cls-1" d=3D"m10.2 19.8v-1.8h-4.02a1=
.9 1.9 0 0 1-2-1.64 7.39 7.39 0 0 1 0.09-3.47 2.06 2.06 0 0 1 2.17-1.75h3.7=
6v-1.74h-4.41a3.83 3.83 0 0 0-3.85 2.85c-0.15 0.51-0.22 1-0.33 1.56v1.74c0 =
0.25 0.09 0.5 0.13 0.75a4 4 0 0 0 3.83 3.47h4.63z"/><path class=3D"cls-1" d=
=3D"m18.71 14.5a2.24 2.24 0 0 0-1.52-2.12 3.54 3.54 0 0 0-1.19-0.23h-4.57v1=
.6h3.92a1.13 1.13 0 0 1 1.29 1.34q0 1.22 0 2.44a0.59 0.59 0 0 1-0.67 0.69h-=
2.27a0.6 0.6 0 0 1-0.7-0.75 0.66 0.66 0 0 1 0.62-0.73 3.12 3.12 0 0 1 0.56-=
0.05h2v-1.52c-0.65 0-2.32-0.06-3.45 0.07a1.88 1.88 0 0 0-1.82 1.92 2 2 0 0 =
0 2 2.6 28.12 28.12 0 0 0 3.94 0 1.88 1.88 0 0 0 1.92-2.06c0-1.06-0.01-2.13=
-0.06-3.2z"/><path class=3D"cls-1" d=3D"m26.69 13.89a6.34 6.34 0 0 1 1-0.09=
h2.25v-1.68h-3.59a3 3 0 0 0-2.62 1.6 4.38 4.38 0 0 0-0.2 4 3 3 0 0 0 2.64 2=
c1.19 0.09 2.38 0.05 3.57 0.06h0.18v-1.63h-2.75a1.69 1.69 0 0 1-1.8-1.45 5.=
3 5.3 0 0 1 0-1.52 1.44 1.44 0 0 1 1.32-1.29z"/><path class=3D"cls-1" d=3D"=
m35.85 12.14v6h-1.56c-1.15 0-1.4-0.25-1.4-1.39q0-2.1 0-4.21v-0.41h-2v5.17a2=
.28 2.28 0 0 0 2.38 2.5c1.24 0.08 2.49 0 3.74 0.06h0.87v-7.72h-2z"/><path c=
lass=3D"cls-1" d=3D"m54.51 18.24a1 1 0 0 1-1-1 3.75 3.75 0 0 1 0-0.43q0-1.3=
9 0-2.77v-0.35h2.1v-1.58h-2.16v-2.69h-2.06v0.38q0 1.77 0 3.54v4.24a2.09 2.0=
9 0 0 0 2.31 2.24h1.86v-1.57c-0.36 0-0.71 0.02-1.05-0.01z"/><path class=3D"=
cls-1" d=3D"m48.5 12.29a3.46 3.46 0 0 0-0.92-0.15h-4.58v1.6h3.92a1.13 1.13 =
0 0 1 1.29 1.31v2.47a0.59 0.59 0 0 1-0.67 0.69h-2.3a0.56 0.56 0 0 1-0.64-0.=
58 0.64 0.64 0 0 1 0.56-0.81 4.56 4.56 0 0 1 0.84-0.05h1.8v-1.58c-0.8 0-2.2=
6-0.05-3.37 0a1.77 1.77 0 0 0-1.7 1.14 4.19 4.19 0 0 0-0.24 1.67 1.78 1.78 =
0 0 0 1.77 1.75 34.06 34.06 0 0 0 4 0 1.86 1.86 0 0 0 2-2 27 27 0 0 0 0-3.3=
7 2.24 2.24 0 0 0-1.76-2.09z"/><path class=3D"cls-1" d=3D"m20.13 19.78h2.09=
v-10.37h-2.09z"/><path class=3D"cls-1" d=3D"m39.33 19.79h2.08v-10.38h-2.08z=
"/><path class=3D"cls-1" d=3D"m71.21 12.15c-1.34 0-2.67-0.08-4 0a2.39 2.39 =
0 0 0-2.41 2.55c-0.05 1.63 0 3.27 0 4.91v0.16h2.1v-6.07h4.31z"/><path class=
=3D"cls-1" d=3D"m61.49 12.19a4.93 4.93 0 0 0-2.18-0.19 3.62 3.62 0 0 0-3.07=
 2.15 4.6 4.6 0 0 0-0.32 2.41 3.61 3.61 0 0 0 2.52 3.14c0.19 0.06 0.34 0.11=
 0.53 0.15a4.45 4.45 0 0 0 2 0 2.85 2.85 0 0 0 0.33-0.07 3.47 3.47 0 0 0 2.=
63-2.66 5.63 5.63 0 0 0 0.07-1.9 3.47 3.47 0 0 0-2.51-3.03zm0.37 4.41a1.84 =
1.84 0 0 1-1.86 1.68 1.77 1.77 0 0 1-1.91-1.62 4.25 4.25 0 0 1 0-1.45 1.76 =
1.76 0 0 1 2-1.54 1.83 1.83 0 0 1 1.79 1.73v0.61c0 0.19 0.02 0.4-0.02 0.59z=
"/></g><g transform=3D"matrix(2.0996 0 0 2.1134 -3.605 -20.098)" fill=3D"#c=
f9"><path class=3D"cls-2" d=3D"m91.63 12.63a4.13 4.13 0 0 0-0.89-0.11h-4a2 =
2 0 0 0-2.06 1.89 16.29 16.29 0 0 0 0 3.16 2.4 2.4 0 0 0 2 2.1l0.56 0.12a3.=
37 3.37 0 0 0 0.65 0.06h4.75v-1.74h-3.88a4.94 4.94 0 0 1-0.89-0.11 1.06 1.0=
6 0 0 1-1-1.11v-2a0.55 0.55 0 0 1 0.55-0.64c0.9-0.05 1.8-0.06 2.7-0.05a0.54=
 0.54 0 0 1 0.52 0.55 0.57 0.57 0 0 1-0.5 0.58 1.79 1.79 0 0 1-0.4 0h-2.35v=
1.64a23.3 23.3 0 0 0 4.19-0.16 1.79 1.79 0 0 0 1.42-1.41 4.41 4.41 0 0 0 0-=
1.29 1.67 1.67 0 0 0-1.37-1.48z"/><path class=3D"cls-2" d=3D"m97.2 18.15a1 =
1 0 0 1-1.1-1.1v-2.84h2.27v-1.7h-2.3v-3h-2.27v7.47a5.54 5.54 0 0 0 0.07 0.9=
3 2.09 2.09 0 0 0 1.53 1.79 3.33 3.33 0 0 0 1 0.14h2v-1.69c-0.4 0-0.8 0.03-=
1.2 0z"/><path class=3D"cls-2" d=3D"m70.3 19.89h2.07v-2h-2.07z"/><path clas=
s=3D"cls-2" d=3D"m83.59 19.76v-4.68a2.31 2.31 0 0 0-1.88-2.39 4.11 4.11 0 0=
 0-1-0.14h-4.86v7.37h2.25v-5.5h2a1 1 0 0 1 1.21 1.22v4.26h2.29s-0.01-0.1-0.=
01-0.14z"/></g></svg>

------MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.calculator.net/style.css

@charset "utf-8";

body, p, td, div, span, input, th, li, textarea { font-family: arial, helve=
tica, sans-serif; font-size: 16px; color: rgb(0, 0, 0); }

body { background: rgb(255, 255, 255); margin: 0px; padding: 0px; border: 0=
px; text-align: center; }

p { margin: 5px 0px 8px; }

img { border: 0px; }

h1 { color: rgb(0, 51, 102); font-size: 26px; font-weight: bold; padding: 0=
px; margin: 12px 0px; }

h2 { font-size: 22px; font-weight: bold; color: rgb(0, 51, 102); padding: 0=
px; margin-bottom: 2px; }

h3 { font-size: 19px; font-weight: bold; }

hr { border: 0px; color: rgb(170, 170, 170); background-color: rgb(170, 170=
, 170); height: 1px; }

a { color: rgb(0, 102, 153); text-decoration: underline; }

a:hover { text-decoration: none; }

input { padding: 5px; color: rgb(0, 0, 0); box-sizing: border-box; }

select { padding: 4px; color: rgb(0, 0, 0); box-sizing: border-box; }

option { font-size: 16px; }

input[type=3D"text"], input[type=3D"url"], input[type=3D"tel"], input[type=
=3D"number"], input[type=3D"color"], input[type=3D"date"], input[type=3D"em=
ail"], select { border: 1px solid rgb(4, 66, 132); border-radius: 2px; box-=
shadow: rgb(102, 102, 102) 1px 1px 2px; font-size: 16px; background-color: =
rgb(255, 255, 255); }

input[type=3D"submit"] { border: 0px; color: rgb(255, 255, 255); padding: 1=
1px 50px 11px 16px; font-size: 16px; font-weight: bold; background-color: r=
gb(76, 123, 37); background-image: url("data:image/svg+xml;utf8,<svg xmlns=
=3D\"http://www.w3.org/2000/svg\" width=3D\"180px\" height=3D\"40px\"><circ=
le cx=3D\"112\" cy=3D\"20\" r=3D\"11\" fill=3D\"darkseagreen\" /><path d=3D=
\"M110 12 L120 20 L110 28 Z\" fill=3D\"white\" /></svg>"); background-repea=
t: no-repeat; }

input[type=3D"submit"]:hover { background-color: rgb(68, 68, 68); }

input[type=3D"reset"], input[type=3D"button"] { border: 0px; color: rgb(255=
, 255, 255); padding: 11px 8px; font-size: 16px; background: rgb(171, 171, =
171); }

input[type=3D"reset"]:hover, input[type=3D"button"]:hover { background: rgb=
(68, 68, 68); }

input[type=3D"image"], input[type=3D"image"]:hover { background: rgb(81, 13=
2, 40); color: rgb(255, 255, 255); padding: 0px; margin: 0px; }

.clearbtn { cursor: pointer; }

.inputErrMsg { position: absolute; padding: 4px 8px; color: rgb(0, 0, 0); b=
ackground-color: rgb(255, 204, 204); border: 1px solid rgb(255, 170, 170); =
white-space: nowrap; display: inline-block; }

.cbcontainer { display: inline-block; position: relative; padding-left: 28p=
x; padding-top: 1px; margin: 5px 0px; cursor: pointer; font-size: 16px; use=
r-select: none; }

.cbcontainer input { position: absolute; opacity: 0; cursor: pointer; heigh=
t: 0px; width: 0px; }

.cbmark { position: absolute; top: 0px; left: 0px; height: 16px; width: 16p=
x; background-color: rgb(255, 255, 255); border: 2px solid rgb(51, 102, 153=
); }

.rbmark { position: absolute; top: 0px; left: 0px; height: 16px; width: 16p=
x; background-color: rgb(255, 255, 255); border: 2px solid rgb(51, 102, 153=
); border-radius: 50%; }

.cbcontainer:hover input ~ .cbmark, .cbcontainer:hover input ~ .rbmark { ba=
ckground-color: rgb(204, 204, 204); }

.cbcontainer input:checked ~ .cbmark, .cbcontainer input:checked ~ .rbmark =
{ background-color: rgb(51, 102, 153); }

.cbmark::after, .rbmark::after { content: ""; position: absolute; display: =
none; }

.cbcontainer input:checked ~ .cbmark::after, .cbcontainer input:checked ~ .=
rbmark::after { display: block; }

.cbcontainer .cbmark::after { left: 4px; top: 0px; width: 5px; height: 10px=
; border-style: solid; border-color: white; border-image: initial; border-w=
idth: 0px 3px 3px 0px; transform: rotate(45deg); }

.cbcontainer .rbmark::after { top: 4px; left: 4px; width: 8px; height: 8px;=
 border-radius: 50%; background: white; }

.indollar { background-image: url("data:image/svg+xml;utf8,<svg xmlns=3D\"h=
ttp://www.w3.org/2000/svg\" width=3D\"15px\" height=3D\"20px\"><text x=3D\"=
2\" y=3D\"15\" style=3D\"font: normal 16px arial;\">$</text></svg>"); backg=
round-position: left center; background-repeat: no-repeat; padding-left: 11=
px !important; }

.inpct { background-image: url("data:image/svg+xml;utf8,<svg xmlns=3D\"http=
://www.w3.org/2000/svg\" width=3D\"17px\" height=3D\"20px\"><text x=3D\"1\"=
 y=3D\"15\" style=3D\"font: normal 16px arial;\">%</text></svg>"); backgrou=
nd-position: right center; background-repeat: no-repeat; padding-right: 18p=
x !important; }

.innormal { width: 90px; }

.in4char { width: 58px; }

.in3char { width: 46px; }

.in2char { width: 35px; }

.inlong { width: 120px; }

.inlonger { width: 170px; }

.inlongest { width: 230px; }

.inlongesthalf { width: 112px; }

.infull { width: 226px; }

.inhalf { width: 110px; }

.infulltxarea { width: 600px; padding: 8px; }

.inshortfull { width: 170px; }

.inshorthalf { width: 82px; }

.inuiyear { padding-right: 50px; }

.inuiyearspan { margin-left: -45px; color: rgb(136, 136, 136); }

.inuipound { padding-right: 62px; }

.inuipoundspan { margin-left: -58px; color: rgb(136, 136, 136); }

.inuifoot { padding-right: 38px; }

.inuifootspan { margin-left: -34px; color: rgb(136, 136, 136); }

.inuiinch { padding-right: 57px; }

.inuiinchspan { margin-left: -53px; color: rgb(136, 136, 136); }

.inuick { padding-right: 32px; }

.inuickspan { margin-left: -27px; color: rgb(136, 136, 136); }

.inui1c { padding-right: 16px; }

.inui1cspan { margin-left: -11px; color: rgb(136, 136, 136); }

.scaleimg { max-width: 100%; height: auto; }

#tt { position: absolute; display: block; background-color: rgb(71, 71, 71)=
; color: rgb(255, 255, 255); padding: 8px; border: 1px solid rgb(0, 0, 0); =
text-align: left; }

.ttimg { opacity: 0.4; vertical-align: top; }

.ttimg:hover { opacity: 1; }

#headerout { background: rgb(0, 51, 102); text-align: center; }

#header { width: 1100px; height: 60px; background: rgb(0, 51, 102); padding=
: 0px; margin-left: auto; margin-right: auto; text-align: left; overflow: h=
idden; }

#logo { padding: 18px 0px; width: 270px; float: left; }

#login { padding: 2px; float: right; color: rgb(204, 204, 204); }

#login a { color: rgb(204, 204, 204); text-decoration: none; }

#login a:hover { text-decoration: underline; }

.topNavAbs { position: absolute; top: 21px; left: 50%; width: 520px; margin=
-left: -80px; text-align: left; }

.topNavAbs a { color: white; padding: 10px 16px; border: none; cursor: poin=
ter; font-size: 16px; text-transform: uppercase; display: inline-block; tex=
t-decoration: none; }

.topNavAbs a:hover { background-color: rgb(81, 132, 40); }

.topNavOn { background-color: rgb(81, 132, 40); }

#contentout { width: 1100px; padding-top: 5px; margin-left: auto; margin-ri=
ght: auto; text-align: left; overflow: auto; }

#content { padding: 0px 0px 15px; width: 728px; float: left; }

#right { width: 336px; float: right; text-align: center; }

#contentbig { padding: 0px 0px 15px; width: 843px; float: right; }

#footer { background: rgb(225, 225, 225); padding: 25px 0px; font-size: 13p=
x; color: rgb(85, 85, 85); text-align: center; }

#footer a { color: rgb(68, 68, 68); }

#footer a:hover { text-decoration: none; }

#footerin { width: 1100px; margin-left: auto; margin-right: auto; text-alig=
n: left; overflow: auto; color: rgb(85, 85, 85); }

#footernav { text-align: center; }

#homecaldiv { background: rgb(209, 221, 233); padding: 10px 0px; }

#homelistdiv { background: rgb(255, 255, 255); padding: 20px 0px; }

#homecaldiv td { overflow: hidden; }

#homelistwrap { display: grid; row-gap: 30px; justify-content: center; grid=
-template-columns: 280px 320px 260px 220px; }

#breadcrumbs, #breadcrumbs span { font-size: 13px; }

#breadcrumbs a, #breadcrumbs a span { text-decoration: none; color: rgb(0, =
102, 153); }

#breadcrumbs a:hover, #breadcrumbs a span:hover { text-decoration: underlin=
e; }

#othercalc { border: 1px solid rgb(51, 102, 153); margin: auto; text-align:=
 left; width: 332px; }

#octitle { background-color: rgb(51, 102, 153); padding: 6px; color: rgb(25=
5, 255, 255); font-size: 18px; font-weight: bold; }

#octitle a { color: rgb(255, 255, 255); text-decoration: none; }

#octitle a:hover { text-decoration: underline; }

#occontent { padding: 3px 6px; font-size: 14px; }

#occontent a { display: inline-block; width: 158px; padding: 3px 0px; }

#ocother { background-color: rgb(221, 221, 221); padding: 6px; text-align: =
center; font-size: 15px; color: rgb(187, 187, 187); }

#sectitle { background-color: rgb(51, 102, 153); padding: 6px; color: rgb(2=
55, 255, 255); font-size: 18px; font-weight: bold; }

.hicon { padding: 20px 0px 20px 10px; }

.hl { list-style-type: none; margin: 0px; padding: 5px 0px 5px 8px; backgro=
und-color: rgb(255, 255, 255); font-size: 16px; }

.hl li { padding: 0px 0px 8px; }

.hl li a { text-decoration: none; }

.hl li a:hover { text-decoration: underline; }

.hh { color: rgb(35, 131, 43); padding: 8px 5px; font-size: 22px; }

.hh a { color: rgb(35, 131, 43); text-decoration: none; }

.hh a:hover { text-decoration: underline; }

.smtb a { text-decoration: underline; }

.smtb a:hover { text-decoration: none; }

.smtbtop a { text-decoration: none; }

.smtbtop a:hover { text-decoration: underline; }

.smalltext { font-size: 13px; }

.bigtext { font-size: 18px; }

.verybigtext { font-size: 23px; }

.morelinespace { line-height: 125%; }

.inlinetable { display: inline; }

table.cinfoT { border-collapse: collapse; border-spacing: 0px; margin-top: =
0px; }

table.cinfoT th, table.cinfoT td.cinfoHd, table.cinfoT td.cinfoHdL { border=
-width: 1px; border-style: solid; border-color: rgb(17, 68, 119) rgb(17, 68=
, 119) rgb(51, 102, 153); background-color: rgb(51, 102, 153); font-weight:=
 bold; color: rgb(255, 255, 255); padding: 5px 3px; }

table.cinfoT td { border: 1px solid rgb(204, 204, 204); color: rgb(0, 0, 0)=
; padding: 3px; }

table.cinfoT tr:nth-child(2n+1) { background-color: rgb(238, 238, 238); }

table.cinfoT tr:nth-child(2n) { background-color: rgb(255, 255, 255); }

table.cinfoTS td.cinfoHd { font-size: 13px; }

table.cinfoTS td.cinfoHdL { font-size: 13px; }

table.cinfoTS td { font-size: 13px; padding: 3px 1px; }

.frac { display: inline-block; text-align: center; vertical-align: middle; =
}

.fracnum { display: block; }

.fracden { display: block; border-top: 1px solid rgb(0, 0, 0); padding: 0px=
 3px; }

#topmenu ul { color: rgb(0, 0, 0); border-bottom: 1px solid rgb(187, 187, 1=
87); margin: 12px 0px 0px; padding: 0px 0px 8px; font-size: 15px; font-weig=
ht: bold; }

#topmenu ul li { display: inline; overflow: hidden; list-style-type: none; =
margin-left: 0px; }

#topmenu ul li a, #topmenu ul li a:visited { color: rgb(255, 255, 255); bac=
kground: rgb(51, 102, 153); border: 1px solid rgb(51, 102, 153); padding: 8=
px 5px; margin: 0px; text-decoration: none; }

#topmenu ul li a:hover { background: rgb(238, 238, 238); color: rgb(0, 0, 0=
); }

#topmenu ul #menuon a { color: rgb(0, 0, 0); background: rgb(238, 238, 238)=
; border-width: 1px 1px 2px; border-style: solid; border-color: rgb(187, 18=
7, 187) rgb(187, 187, 187) rgb(238, 238, 238); border-image: initial; paddi=
ng: 8px 5px; margin: 0px; text-decoration: none; }

#topmenu ul #menuon a:hover { background: rgb(238, 238, 238); }

.topmenucenter { }

#insmd { background-color: rgb(51, 102, 153); margin-bottom: 3px; }

#insmdc { background-color: rgb(51, 102, 153); margin-bottom: 3px; text-ali=
gn: center; }

fieldset { margin-top: 10px; padding: 0px 10px 5px; border: 0px solid rgb(1=
89, 210, 218); background: rgb(238, 238, 238); color: rgb(238, 238, 238); }

legend { font-size: 18px; font-weight: bold; padding: 5px 15px; background:=
 rgb(238, 238, 238); color: rgb(0, 0, 0); }

fieldset a { display: inline-block; white-space: nowrap; padding: 6px; font=
-size: 16px; background: rgb(51, 102, 153); color: rgb(255, 255, 255); marg=
in-bottom: 5px; text-decoration: none; }

fieldset a:hover { background: rgb(65, 117, 22); color: rgb(255, 255, 255);=
 }

fieldset div { display: inline-block; white-space: nowrap; padding: 10px; f=
ont-size: 18px; background: rgb(4, 66, 132); color: rgb(255, 255, 255); mar=
gin-bottom: 5px; border-radius: 3px; text-decoration: none; }

fieldset div:hover { background: rgb(196, 119, 81); color: rgb(255, 255, 25=
5); }

.arrow_box { position: relative; background: rgb(238, 238, 238); border: 1p=
x solid rgb(170, 170, 170); padding: 3px 8px; text-align: center; }

.arrow_box::after, .arrow_box::before { left: 100%; top: 50%; border: solid=
 transparent; content: " "; height: 0px; width: 0px; position: absolute; po=
inter-events: none; }

.arrow_box::after { border-color: rgba(221, 221, 221, 0) rgba(221, 221, 221=
, 0) rgba(221, 221, 221, 0) rgb(238, 238, 238); border-width: 12px; margin-=
top: -12px; }

.arrow_box::before { border-color: rgba(238, 238, 238, 0) rgba(238, 238, 23=
8, 0) rgba(238, 238, 238, 0) rgb(170, 170, 170); border-width: 13px; margin=
-top: -13px; }

.result_box { background: rgb(227, 237, 218); border: 1px solid rgb(141, 18=
0, 109); padding: 3px 8px; text-align: center; }

.panel { background: rgb(238, 238, 238); border: 1px solid rgb(187, 187, 18=
7); padding: 5px; }

.panel2 { background-color: rgb(238, 238, 238); padding: 5px; border-right:=
 1px solid rgb(187, 187, 187); border-bottom: 1px solid rgb(187, 187, 187);=
 border-left: 1px solid rgb(187, 187, 187); }

.reference { font-size: 13px; padding-left: 1.8em; }

.reference li { font-size: 13px; overflow-wrap: break-word; }

#printit { width: 80px; float: right; text-align: right; }

.h2result { background: rgb(81, 132, 40); color: rgb(255, 255, 255); border=
: 1px solid rgb(81, 132, 40); padding: 5px; margin-top: 3px; font-size: 22p=
x; font-weight: normal; }

.h3head { margin-bottom: 2px; }

.sectionlists { }

.sectionlists div { padding-bottom: 5px; }

#searchbox { padding-top: 16px; }

#bluebtn { border-radius: 1px; background: rgb(51, 102, 153); padding: 5px =
8px; font-size: 18px; color: rgb(255, 255, 255); }

#bluebtn:hover { background: rgb(68, 68, 68); color: rgb(255, 255, 255); }

#calcSearchOut { padding: 5px; }

#calcSearchOut div { padding: 5px; text-align: left; }

.leftinput { width: 325px; float: left; }

.rightresult { width: 375px; float: right; }

.clefthalf { width: 350px; float: left; }

.crighthalf { width: 350px; float: right; }

.espaceforM { display: none; }

#clear { margin-left: auto; margin-right: auto; clear: both; height: 0px; }

.leftchart { padding-top: 10px; width: 500px; float: left; }

.rightpie { padding-top: 10px; width: 165px; float: right; }

@media (max-width: 1140px) {
  #header { width: 990px; padding-left: 8px; }
  #contentout { width: 1000px; }
  #content { width: 640px; float: left; padding-left: 10px; }
  #footerin { width: 990px; }
  #homelistwrap { display: grid; row-gap: 30px; grid-template-columns: 270p=
x 300px 220px 210px; }
  .leftinput, .clefthalf, .crighthalf { width: 310px; }
  .rightresult { width: 320px; }
  .leftchart { width: 445px; }
  .rightpie { width: 155px; }
}

@media (max-width: 720px) {
  #header { width: auto; padding: 0px 8px; }
  #contentout { width: auto; padding: 8px; }
  #content { float: none; width: auto; padding: 0px; }
  #homelistwrap { grid-template-columns: 320px 320px; }
  #right { width: auto; float: none; }
  #footerin { width: auto; }
  .topNavAbs { display: none; }
  .espaceforM { display: block; }
}

@media (max-width: 650px) {
  #homelistwrap { grid-template-columns: 250px 250px; }
  .leftinput, .rightresult, .clefthalf, .crighthalf { width: auto; float: n=
one; }
  img { max-width: 100%; height: auto; }
}

@media (max-width: 490px) {
  #homelistwrap { grid-template-columns: auto; }
}
------MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://securepubads.g.doubleclick.net/static/topics/topics_frame.html

<!DOCTYPE html><html><head><meta http-equiv=3D"Content-Type" content=3D"tex=
t/html; charset=3DUTF-8">
   =20
    <title>Topics Frame</title>
    <meta http-equiv=3D"origin-trial" content=3D"Avh5Ny0XEFCyQ7+oNieXskUrqY=
8edUzL5/XrwKlGjARQHW4TFRK+jVd5HnDIpY20n5OLHfgU4ku7x48N3uhG/A0AAABxeyJvcmlna=
W4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRi=
b3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZX0=3D">
   =20
  </head>
  <body>

</body></html>
------MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.google.com/recaptcha/api2/aframe

<!DOCTYPE html><html><head><meta http-equiv=3D"Content-Type" content=3D"tex=
t/html; charset=3DUTF-8"></head><body><img src=3D"https://pagead2.googlesyn=
dication.com/pagead/sodar?id=3Dsodar2&amp;v=3D237&amp;li=3Dgpt_m20250612010=
1&amp;jk=3D4707977620932317&amp;rc=3D"></body></html>
------MultipartBoundary--iQ3YCczSWAb9bibCUsoS6gmfhjKACWYHxqmy1i0EHZ------
