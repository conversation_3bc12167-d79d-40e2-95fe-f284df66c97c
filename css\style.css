/* Calculator.net Style - Dry Calculator Theme */
@charset "utf-8";

body, p, td, div, span, input, th, li, textarea { 
    font-family: arial, helvetica, sans-serif; 
    font-size: 16px; 
    color: rgb(0, 0, 0); 
}

body { 
    background: rgb(255, 255, 255); 
    margin: 0px; 
    padding: 0px; 
    border: 0px; 
    text-align: center; 
}

p { 
    margin: 5px 0px 8px; 
}

img { 
    border: 0px; 
}

h1 { 
    color: rgb(0, 51, 102); 
    font-size: 26px; 
    font-weight: bold; 
    padding: 0px; 
    margin: 12px 0px; 
}

h2 { 
    font-size: 22px; 
    font-weight: bold; 
    color: rgb(0, 51, 102); 
    padding: 0px; 
    margin-bottom: 2px; 
}

h3 { 
    font-size: 19px; 
    font-weight: bold; 
}

hr { 
    border: 0px; 
    color: rgb(170, 170, 170); 
    background-color: rgb(170, 170, 170); 
    height: 1px; 
}

a { 
    color: rgb(0, 102, 153); 
    text-decoration: underline; 
}

a:hover { 
    text-decoration: none; 
}

/* Header Styles */
#headerout {
    background: rgb(0, 51, 102);
    height: 50px;
    width: 100%;
}

#header {
    width: 1000px;
    margin: 0 auto;
    position: relative;
    height: 50px;
}

#logo {
    float: left;
    padding-top: 14px;
}

#login {
    float: right;
    padding-top: 16px;
}

#login a {
    color: white;
    text-decoration: none;
    font-size: 14px;
}

#clear {
    clear: both;
}

/* Calculator Styles */
#homecaldiv {
    background: rgb(245, 245, 245);
    padding: 20px 0;
}

#contentout {
    width: 1000px;
    margin: 0 auto;
    text-align: left;
}

#sciout {
    background: white;
    border: 2px solid rgb(0, 51, 102);
    border-radius: 5px;
    margin: 10px;
}

#sciInPut {
    background: rgb(240, 240, 240);
    padding: 10px;
    min-height: 20px;
    font-family: monospace;
    font-size: 14px;
}

#sciOutPut {
    background: white;
    padding: 15px;
    font-size: 24px;
    font-weight: bold;
    text-align: right;
    border-bottom: 1px solid rgb(200, 200, 200);
}

/* Scientific Calculator Button Styles */
.scifunc, .scinm, .sciop, .scieq {
    display: inline-block;
    width: 50px;
    height: 35px;
    margin: 2px;
    text-align: center;
    line-height: 35px;
    cursor: pointer;
    border-radius: 3px;
    font-weight: bold;
}

.scifunc {
    background: rgb(200, 220, 255);
    border: 1px solid rgb(150, 180, 220);
}

.scinm {
    background: rgb(240, 240, 240);
    border: 1px solid rgb(180, 180, 180);
}

.sciop {
    background: rgb(255, 220, 200);
    border: 1px solid rgb(220, 180, 150);
}

.scieq {
    background: rgb(76, 123, 37);
    color: white;
    border: 1px solid rgb(60, 100, 30);
}

.scifunc:hover, .scinm:hover, .sciop:hover, .scieq:hover {
    opacity: 0.8;
}

/* Home Search */
#homesch {
    padding: 20px;
}

#homesch h1 {
    margin-bottom: 20px;
}

/* Input Styles */
input[type="text"], input[type="url"], input[type="tel"], input[type="number"], 
input[type="color"], input[type="date"], input[type="email"], select { 
    border: 1px solid rgb(4, 66, 132); 
    border-radius: 2px; 
    box-shadow: rgb(102, 102, 102) 1px 1px 2px; 
    font-size: 16px; 
    background-color: rgb(255, 255, 255); 
    padding: 5px;
    color: rgb(0, 0, 0);
    box-sizing: border-box;
}

input[type="submit"] { 
    border: 0px; 
    color: rgb(255, 255, 255); 
    padding: 11px 50px 11px 16px; 
    font-size: 16px; 
    font-weight: bold; 
    background-color: rgb(76, 123, 37); 
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='180px' height='40px'><circle cx='112' cy='20' r='11' fill='darkseagreen' /><path d='M110 12 L120 20 L110 28 Z' fill='white' /></svg>"); 
    background-repeat: no-repeat; 
    border-radius: 3px;
    cursor: pointer;
}

input[type="submit"]:hover { 
    background-color: rgb(68, 68, 68); 
}

input[type="reset"], input[type="button"] { 
    border: 0px; 
    color: rgb(255, 255, 255); 
    padding: 11px 8px; 
    font-size: 16px; 
    background: rgb(171, 171, 171); 
    border-radius: 3px;
    cursor: pointer;
}

input[type="reset"]:hover, input[type="button"]:hover { 
    background: rgb(68, 68, 68); 
}

.inlongest { 
    width: 230px; 
}

#bluebtn {
    background: rgb(0, 102, 153);
    color: white;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
}

#bluebtn:hover {
    background: rgb(0, 80, 120);
}

/* Home List Styles */
#homelistdiv {
    background: white;
    padding: 30px 0;
}

#homelistwrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 20px;
}

.homelisttile {
    width: 240px;
    text-align: center;
}

.hicon {
    margin-bottom: 15px;
}

.hicon img {
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.hh {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
}

.hh a {
    color: rgb(0, 51, 102);
    text-decoration: none;
}

.hh a:hover {
    text-decoration: underline;
}

.hl {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.hl li {
    margin-bottom: 5px;
    padding-left: 15px;
    position: relative;
}

.hl li:before {
    content: "▶";
    position: absolute;
    left: 0;
    color: rgb(0, 102, 153);
    font-size: 10px;
    top: 2px;
}

.hl a {
    font-size: 14px;
    color: rgb(0, 102, 153);
}

/* Footer */
#footer {
    background: rgb(240, 240, 240);
    padding: 30px 0;
    border-top: 1px solid rgb(200, 200, 200);
}

#footerin {
    width: 1000px;
    margin: 0 auto;
    text-align: left;
    font-size: 14px;
    line-height: 1.6;
}

#footernav {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgb(200, 200, 200);
}

#footernav a {
    color: rgb(0, 102, 153);
    margin-right: 10px;
}

/* Sub-page Styles */
#content {
    width: 700px;
    float: left;
    padding: 20px;
    text-align: left;
}

#right {
    width: 280px;
    float: right;
    padding: 20px;
}

#breadcrumbs {
    font-size: 14px;
    margin-bottom: 15px;
    color: rgb(100, 100, 100);
}

#breadcrumbs a {
    color: rgb(0, 102, 153);
    text-decoration: none;
}

#breadcrumbs a:hover {
    text-decoration: underline;
}

.panel {
    background: rgb(250, 250, 250);
    border: 1px solid rgb(200, 200, 200);
    border-radius: 5px;
    padding: 20px;
    margin: 20px 0;
}

#calinputtable {
    width: 100%;
    border-collapse: collapse;
}

#calinputtable td {
    padding: 8px;
    vertical-align: middle;
}

#calinputtable td:first-child {
    width: 150px;
    font-weight: bold;
    text-align: right;
    padding-right: 15px;
}

.cbcontainer {
    display: inline-block;
    position: relative;
    padding-left: 28px;
    padding-top: 1px;
    margin: 5px 0px;
    cursor: pointer;
    font-size: 16px;
    user-select: none;
}

.cbcontainer input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0px;
    width: 0px;
}

.rbmark {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 16px;
    width: 16px;
    background-color: rgb(255, 255, 255);
    border: 2px solid rgb(51, 102, 153);
    border-radius: 50%;
}

.cbcontainer:hover input ~ .rbmark {
    background-color: rgb(204, 204, 204);
}

.cbcontainer input:checked ~ .rbmark {
    background-color: rgb(51, 102, 153);
}

.rbmark::after {
    content: "";
    position: absolute;
    display: none;
}

.cbcontainer input:checked ~ .rbmark::after {
    display: block;
}

.cbcontainer .rbmark::after {
    top: 4px;
    left: 4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
}

.infull {
    width: 226px;
}

.inuipound {
    padding-right: 62px;
}

.inuipoundspan {
    margin-left: -58px;
    color: rgb(136, 136, 136);
}

fieldset {
    border: 1px solid rgb(200, 200, 200);
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
}

fieldset legend {
    font-weight: bold;
    padding: 0 10px;
    color: rgb(0, 51, 102);
}

#searchbox {
    width: 100%;
    margin: 20px 0;
}

#othercalc {
    background: rgb(250, 250, 250);
    border: 1px solid rgb(200, 200, 200);
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
}

#octitle {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 10px;
    border-bottom: 1px solid rgb(200, 200, 200);
    padding-bottom: 5px;
}

#octitle a {
    color: rgb(0, 51, 102);
    text-decoration: none;
}

#occontent {
    margin: 10px 0;
}

#occontent a {
    display: block;
    padding: 3px 0;
    font-size: 14px;
    color: rgb(0, 102, 153);
    text-decoration: none;
}

#occontent a:hover {
    text-decoration: underline;
}

#ocother {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgb(200, 200, 200);
    font-size: 14px;
}

#ocother a {
    color: rgb(0, 102, 153);
}

#insmdc {
    text-align: center;
    margin: 20px 0;
}

/* Category Page Styles */
.calc-category {
    background: rgb(250, 250, 250);
    border: 1px solid rgb(200, 200, 200);
    border-radius: 5px;
    padding: 20px;
}

.calc-category h3 {
    color: rgb(0, 51, 102);
    margin-top: 0;
    margin-bottom: 15px;
    border-bottom: 2px solid rgb(0, 102, 153);
    padding-bottom: 5px;
}

.calc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.calc-list li {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgb(230, 230, 230);
}

.calc-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.calc-list li a {
    font-size: 16px;
    font-weight: bold;
    color: rgb(0, 102, 153);
    text-decoration: none;
    display: block;
    margin-bottom: 5px;
}

.calc-list li a:hover {
    text-decoration: underline;
}

.calc-list li p {
    margin: 0;
    font-size: 14px;
    color: rgb(100, 100, 100);
    line-height: 1.4;
}

.temp-unit {
    margin-left: 5px;
    color: rgb(136, 136, 136);
}

/* Responsive Design */
@media (max-width: 1020px) {
    #header, #contentout, #footerin {
        width: 95%;
        padding: 0 2.5%;
    }

    #content {
        width: 100%;
        float: none;
    }

    #right {
        width: 100%;
        float: none;
    }

    #homelistwrap {
        justify-content: center;
    }

    .homelisttile {
        width: 45%;
        margin-bottom: 30px;
    }
}

@media (max-width: 600px) {
    .homelisttile {
        width: 100%;
    }

    #sciout {
        margin: 5px;
    }

    .scifunc, .scinm, .sciop, .scieq {
        width: 40px;
        height: 30px;
        line-height: 30px;
        font-size: 12px;
    }

    #calinputtable td:first-child {
        width: auto;
        text-align: left;
        display: block;
        padding-bottom: 5px;
    }

    #calinputtable td {
        display: block;
        width: 100%;
    }
}
