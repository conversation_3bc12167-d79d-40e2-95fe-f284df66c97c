<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Army Body Fat Calculator Test - Dry Calculator</title>
    <meta name="description" content="Test page for Army Body Fat Calculator functionality and mobile responsiveness.">
    <link rel="stylesheet" href="css/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="images/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/icon-16x16.png">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo">
            <a href="index.html">
                <img src="images/dry-calculator-logo.svg" width="208" height="22" alt="Dry Calculator">
            </a>
        </div>
        <div id="login">
            <!-- <PERSON>gin removed -->
        </div>
    </div>
</div>
<div id="clear"></div>

<div id="contentout">
    <div id="content">
        <div id="breadcrumbs">
            <a href="index.html">home</a> / 
            <a href="army-body-fat-calculator.html">army body fat calculator</a> / 
            <a href="army-body-fat-test.html">test</a>
        </div>
        
        <h1>Army Body Fat Calculator Test</h1>
        <p>This page tests the Army Body Fat Calculator functionality and mobile responsiveness. The calculator is based on the Army Body Fat Assessment for the Army Body Composition Program (June 12, 2023).</p>

        <div id="insmdc">
            <img src="images/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use">
        </div>
        
        <!-- Test Status Display -->
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #dee2e6;">
            <h3 style="margin: 0 0 10px 0; color: #495057;">🧪 Test Status</h3>
            <div id="test-info">
                <p><strong>Device Type:</strong> <span id="device-type">Detecting...</span></p>
                <p><strong>Screen Size:</strong> <span id="screen-size">Detecting...</span></p>
                <p><strong>Calculator Status:</strong> <span id="calculator-status">Loading...</span></p>
            </div>
        </div>
        
        <!-- Test Cases -->
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;">
            <h3 style="margin: 0 0 10px 0; color: #004085;">📋 Test Cases</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                <button onclick="loadTestCase('male_pass')" style="padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Male - Pass (20 yr, 180 lbs)
                </button>
                <button onclick="loadTestCase('male_fail')" style="padding: 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Male - Fail (25 yr, 220 lbs)
                </button>
                <button onclick="loadTestCase('female_pass')" style="padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Female - Pass (22 yr, 140 lbs)
                </button>
                <button onclick="loadTestCase('female_fail')" style="padding: 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Female - Fail (30 yr, 180 lbs)
                </button>
            </div>
        </div>
        
        <form name="calform">
            <div class="panel">
                <table id="calinputtable">
                    <tbody>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="csex1" class="cbcontainer">
                                    <input type="radio" name="csex" id="csex1" value="m" checked="">
                                    <span class="rbmark"></span>male
                                </label> &nbsp;
                                <label for="csex2" class="cbcontainer">
                                    <input type="radio" name="csex" id="csex2" value="f">
                                    <span class="rbmark"></span>female
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <td>Age</td>
                            <td>
                                <input type="text" name="cage" id="cage" value="21" class="infull">
                            </td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td>
                                <input type="text" name="cweight" id="cweight" value="165" class="infull inuipound">
                                <span class="inuipoundspan">pounds</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Waist</td>
                            <td>
                                <table border="0" cellpadding="0" cellspacing="0">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <table border="0" cellpadding="0" cellspacing="0">
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <input type="text" name="cwaistfeet" id="cwaistfeet" value="3" class="inhalf inuifoot">
                                                                <span class="inuifootspan">feet</span>
                                                            </td>
                                                            <td>&nbsp;&nbsp;</td>
                                                            <td>
                                                                <input type="text" name="cwaistinch" id="cwaistinch" value="0" class="inhalf inuiinch">
                                                                <span class="inuiinchspan">inches</span>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td style="padding-left:12px;">
                                                abdominal circumference at the level of belly button
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" align="left" style="padding-top: 8px;padding-left: 70px;">
                                <input type="submit" name="x" value="Calculate">
                                <input type="button" value="Clear">
                                <input type="button" value="Run All Tests" onclick="runAllTests()" style="background: #007bff;">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </form>

        <!-- Results Section -->
        <div id="result-section" style="display: none; margin-top: 20px;">
            <div class="h2result">
                <span id="result-title">Body Fat Calculation Results</span>
            </div>
            <div id="results-content" style="background: #f0f8ff; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
                <!-- Results will be populated here -->
            </div>
        </div>

        <!-- Test Results Log -->
        <div id="test-log" style="display: none; margin-top: 20px;">
            <h3>🧪 Test Results Log</h3>
            <div id="test-log-content" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                <!-- Test log will appear here -->
            </div>
        </div>

        <br><p></p>
        <fieldset>
            <legend>Related</legend>
            <a href="army-body-fat-calculator.html">Army Body Fat Calculator</a> | 
            <a href="bmi-calculator.html">BMI Calculator</a> | 
            <a href="body-fat-calculator.html">Body Fat Calculator</a>
        </fieldset>
        <p></p>
        
        <h2>Test Information</h2>
        <p>This test page validates the Army Body Fat Calculator implementation:</p>
        
        <h3>Test Cases Included:</h3>
        <ul>
            <li><strong>Male Pass:</strong> 20-year-old male, 180 lbs, 32" waist (should pass)</li>
            <li><strong>Male Fail:</strong> 25-year-old male, 220 lbs, 40" waist (should fail)</li>
            <li><strong>Female Pass:</strong> 22-year-old female, 140 lbs, 28" waist (should pass)</li>
            <li><strong>Female Fail:</strong> 30-year-old female, 180 lbs, 36" waist (should fail)</li>
        </ul>
        
        <h3>Features Tested:</h3>
        <ul>
            <li>✅ Army Body Fat calculation formula (2023 standards)</li>
            <li>✅ Age-based maximum allowable percentages</li>
            <li>✅ Gender-specific calculations</li>
            <li>✅ Input validation and error handling</li>
            <li>✅ Mobile responsiveness</li>
            <li>✅ Results display and formatting</li>
            <li>✅ Standards table generation</li>
        </ul>
        
        <h3>Army Standards Reference:</h3>
        <table class="cinfoT" align="center" style="margin: 15px auto;">
            <tbody>
                <tr>
                    <td class="cinfoHd">Age</td>
                    <td class="cinfoHdL">Male</td>
                    <td class="cinfoHdL">Female</td>
                </tr>
                <tr>
                    <td>17-20</td>
                    <td class="cinfoBodL">20%</td>
                    <td class="cinfoBodL">30%</td>
                </tr>
                <tr>
                    <td>21-27</td>
                    <td class="cinfoBodL">22%</td>
                    <td class="cinfoBodL">32%</td>
                </tr>
                <tr>
                    <td>28-39</td>
                    <td class="cinfoBodL">24%</td>
                    <td class="cinfoBodL">34%</td>
                </tr>
                <tr>
                    <td>40 and over</td>
                    <td class="cinfoBodL">26%</td>
                    <td class="cinfoBodL">36%</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688"
                 crossorigin="anonymous"></script>
            <!-- zishiying -->
            <ins class="adsbygoogle"
                 style="display:block"
                 data-ad-client="ca-pub-2205593928173688"
                 data-ad-slot="8971293106"
                 data-ad-format="auto"
                 data-full-width-responsive="true"></ins>
            <script>
                 (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
        
        <form name="calcSearchForm" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td>
                            <input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators...">
                        </td>
                        <td>
                            <span id="bluebtn">Search</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="other-calculators.html">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="army-body-fat-calculator.html">Army Body Fat</a>
                <a href="bmi-calculator.html">BMI</a>
                <a href="body-fat-calculator.html">Body Fat</a>
                <a href="calorie-calculator.html">Calorie</a>
                <a href="ideal-weight-calculator.html">Ideal Weight</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="about-us.html">about us</a> | 
            <a href="sitemap.html">sitemap</a> | 
            <a href="terms.html">terms of use</a> | 
            <a href="privacy.html">privacy policy</a> 
            &nbsp; © 2025 <a href="index.html">drycalculator.net</a>
        </div>
    </div>
</div>

<script src="js/simple-mobile-scroll.js"></script>
<script src="js/army-body-fat-calculator.js"></script>
<script>
// Test functionality
const testCases = {
    male_pass: {
        gender: 'm',
        age: 20,
        weight: 180,
        waistFeet: 2,
        waistInches: 8,
        expectedResult: 'pass'
    },
    male_fail: {
        gender: 'm',
        age: 25,
        weight: 220,
        waistFeet: 3,
        waistInches: 4,
        expectedResult: 'fail'
    },
    female_pass: {
        gender: 'f',
        age: 22,
        weight: 140,
        waistFeet: 2,
        waistInches: 4,
        expectedResult: 'pass'
    },
    female_fail: {
        gender: 'f',
        age: 30,
        weight: 180,
        waistFeet: 3,
        waistInches: 0,
        expectedResult: 'fail'
    }
};

function updateDeviceInfo() {
    const isMobile = window.innerWidth <= 768;
    const calculatorLoaded = window.armyBodyFatCalculator ? 'Loaded' : 'Not Loaded';
    
    document.getElementById('device-type').textContent = isMobile ? 'Mobile/Tablet' : 'Desktop';
    document.getElementById('screen-size').textContent = `${window.innerWidth} x ${window.innerHeight}`;
    document.getElementById('calculator-status').textContent = calculatorLoaded;
}

function loadTestCase(caseId) {
    const testCase = testCases[caseId];
    if (!testCase) return;
    
    // Set gender
    if (testCase.gender === 'm') {
        document.getElementById('csex1').checked = true;
        document.getElementById('csex2').checked = false;
    } else {
        document.getElementById('csex1').checked = false;
        document.getElementById('csex2').checked = true;
    }
    
    // Set values
    document.getElementById('cage').value = testCase.age;
    document.getElementById('cweight').value = testCase.weight;
    document.getElementById('cwaistfeet').value = testCase.waistFeet;
    document.getElementById('cwaistinch').value = testCase.waistInches;
    
    // Log the test case
    logTest(`Loaded test case: ${caseId} - ${testCase.gender === 'm' ? 'Male' : 'Female'}, Age ${testCase.age}, Weight ${testCase.weight}lbs, Waist ${testCase.waistFeet}'${testCase.waistInches}"`);
}

function runAllTests() {
    const testLog = document.getElementById('test-log');
    const testLogContent = document.getElementById('test-log-content');
    
    testLog.style.display = 'block';
    testLogContent.innerHTML = '<div>🧪 Running all test cases...</div>';
    
    let testIndex = 0;
    const testIds = Object.keys(testCases);
    
    function runNextTest() {
        if (testIndex >= testIds.length) {
            logTest('✅ All tests completed!');
            return;
        }
        
        const testId = testIds[testIndex];
        const testCase = testCases[testId];
        
        logTest(`\n--- Running Test ${testIndex + 1}/${testIds.length}: ${testId} ---`);
        loadTestCase(testId);
        
        // Simulate calculation
        setTimeout(() => {
            if (window.armyBodyFatCalculator) {
                window.armyBodyFatCalculator.calculate();
                
                // Check results
                setTimeout(() => {
                    const resultSection = document.getElementById('result-section');
                    if (resultSection && resultSection.style.display !== 'none') {
                        const resultsContent = document.getElementById('results-content');
                        const hasPass = resultsContent.innerHTML.includes('WITHIN STANDARDS');
                        const hasFail = resultsContent.innerHTML.includes('EXCEEDS STANDARDS');
                        
                        const actualResult = hasPass ? 'pass' : (hasFail ? 'fail' : 'unknown');
                        const expected = testCase.expectedResult;
                        const success = actualResult === expected;
                        
                        logTest(`Expected: ${expected}, Actual: ${actualResult} - ${success ? '✅ PASS' : '❌ FAIL'}`);
                    } else {
                        logTest('❌ No results displayed');
                    }
                    
                    testIndex++;
                    setTimeout(runNextTest, 1000);
                }, 500);
            } else {
                logTest('❌ Calculator not loaded');
                testIndex++;
                setTimeout(runNextTest, 1000);
            }
        }, 500);
    }
    
    runNextTest();
}

function logTest(message) {
    const testLogContent = document.getElementById('test-log-content');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${timestamp}] ${message}`;
    
    testLogContent.appendChild(logEntry);
    testLogContent.scrollTop = testLogContent.scrollHeight;
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    updateDeviceInfo();
    
    window.addEventListener('resize', updateDeviceInfo);
    
    // Check calculator loading
    setTimeout(() => {
        updateDeviceInfo();
    }, 1000);
});
</script>
</body>
</html>
