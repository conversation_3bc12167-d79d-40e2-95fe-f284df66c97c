// Army Body Fat Calculator
// Based on the Army Body Fat Assessment for the Army Body Composition Program (June 12, 2023)

class ArmyBodyFatCalculator {
    constructor() {
        this.form = document.querySelector('form[name="calform"]');
        this.resultSection = document.getElementById('result-section');
        this.resultsContent = document.getElementById('results-content');
        this.resultTitle = document.getElementById('result-title');
        
        this.initializeForm();
    }
    
    initializeForm() {
        if (!this.form) return;
        
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.calculate();
        });
        
        const clearBtn = this.form.querySelector('input[type="button"]');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearForm();
            });
        }
        
        // Add input validation
        this.addInputValidation();
    }
    
    addInputValidation() {
        const inputs = this.form.querySelectorAll('input[type="text"]');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.validateInput(input);
            });
        });
    }
    
    validateInput(input) {
        const value = input.value.trim();
        if (value === '') return;
        
        const isValid = !isNaN(value) && parseFloat(value) > 0;
        
        // Visual feedback
        if (isValid) {
            input.style.borderColor = '#28a745';
            input.style.backgroundColor = '#f8fff8';
        } else {
            input.style.borderColor = '#dc3545';
            input.style.backgroundColor = '#fff8f8';
        }
    }
    
    calculate() {
        const data = this.getFormData();
        
        if (!this.validateFormData(data)) {
            this.showError('Please enter valid values for all fields.');
            return;
        }
        
        const results = this.calculateBodyFat(data);
        this.showResults(results);
    }
    
    getFormData() {
        const genderElements = this.form.querySelectorAll('input[name="csex"]');
        let gender = '';
        for (const element of genderElements) {
            if (element.checked) {
                gender = element.value;
                break;
            }
        }
        
        const age = parseFloat(document.getElementById('cage').value);
        const weight = parseFloat(document.getElementById('cweight').value);
        const waistFeet = parseFloat(document.getElementById('cwaistfeet').value);
        const waistInches = parseFloat(document.getElementById('cwaistinch').value);
        
        // Convert waist measurement to total inches
        const waistTotalInches = (waistFeet * 12) + waistInches;
        
        return {
            gender: gender,
            age: age,
            weight: weight,
            waistFeet: waistFeet,
            waistInches: waistInches,
            waistTotalInches: waistTotalInches
        };
    }
    
    validateFormData(data) {
        return (
            data.gender !== '' &&
            !isNaN(data.age) && data.age > 0 && data.age <= 120 &&
            !isNaN(data.weight) && data.weight > 0 && data.weight <= 1000 &&
            !isNaN(data.waistFeet) && data.waistFeet >= 0 &&
            !isNaN(data.waistInches) && data.waistInches >= 0 && data.waistInches < 12 &&
            data.waistTotalInches > 0 && data.waistTotalInches <= 100
        );
    }
    
    calculateBodyFat(data) {
        // Army Body Fat Calculation Formula (2023)
        // Based on circumference-based tape method using abdominal circumference and body weight
        
        let bodyFatPercentage;
        
        if (data.gender === 'm') {
            // Male formula: BF% = (0.5 × waist circumference in inches) - (0.15 × weight in pounds) + 17.5
            bodyFatPercentage = (0.5 * data.waistTotalInches) - (0.15 * data.weight) + 17.5;
        } else {
            // Female formula: BF% = (0.5 × waist circumference in inches) - (0.12 × weight in pounds) + 15.0
            bodyFatPercentage = (0.5 * data.waistTotalInches) - (0.12 * data.weight) + 15.0;
        }
        
        // Ensure body fat percentage is within reasonable bounds
        bodyFatPercentage = Math.max(0, Math.min(60, bodyFatPercentage));
        
        // Get maximum allowable body fat for age and gender
        const maxAllowable = this.getMaxAllowableBodyFat(data.age, data.gender);
        
        // Determine if within standards
        const withinStandards = bodyFatPercentage <= maxAllowable;
        
        // Calculate BMI for additional context
        const heightInches = this.estimateHeightFromWaist(data.waistTotalInches, data.gender);
        const bmi = (data.weight / (heightInches * heightInches)) * 703;
        
        return {
            bodyFatPercentage: bodyFatPercentage,
            maxAllowable: maxAllowable,
            withinStandards: withinStandards,
            bmi: bmi,
            estimatedHeight: heightInches,
            inputData: data
        };
    }
    
    getMaxAllowableBodyFat(age, gender) {
        if (gender === 'm') {
            if (age >= 17 && age <= 20) return 20;
            if (age >= 21 && age <= 27) return 22;
            if (age >= 28 && age <= 39) return 24;
            if (age >= 40) return 26;
        } else {
            if (age >= 17 && age <= 20) return 30;
            if (age >= 21 && age <= 27) return 32;
            if (age >= 28 && age <= 39) return 34;
            if (age >= 40) return 36;
        }
        return 25; // Default fallback
    }
    
    estimateHeightFromWaist(waistInches, gender) {
        // Rough estimation based on typical body proportions
        // This is for BMI calculation context only
        if (gender === 'm') {
            return waistInches * 2.2; // Approximate ratio for males
        } else {
            return waistInches * 2.1; // Approximate ratio for females
        }
    }
    
    showResults(results) {
        if (!this.resultSection || !this.resultsContent) return;
        
        const data = results.inputData;
        const statusClass = results.withinStandards ? 'within-standards' : 'exceeds-standards';
        const statusText = results.withinStandards ? 'WITHIN STANDARDS' : 'EXCEEDS STANDARDS';
        const statusColor = results.withinStandards ? '#28a745' : '#dc3545';
        
        let resultsHTML = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #003366;">Input Values</h4>
                    <p><strong>Gender:</strong> ${data.gender === 'm' ? 'Male' : 'Female'}</p>
                    <p><strong>Age:</strong> ${data.age} years</p>
                    <p><strong>Weight:</strong> ${data.weight} pounds</p>
                    <p><strong>Waist Circumference:</strong> ${data.waistFeet}' ${data.waistInches}" (${data.waistTotalInches.toFixed(1)} inches total)</p>
                </div>
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #003366;">Calculation Results</h4>
                    <p><strong>Body Fat Percentage:</strong> <span style="font-size: 18px; font-weight: bold; color: ${statusColor};">${results.bodyFatPercentage.toFixed(1)}%</span></p>
                    <p><strong>Maximum Allowable:</strong> ${results.maxAllowable}%</p>
                    <p><strong>Status:</strong> <span style="font-weight: bold; color: ${statusColor};">${statusText}</span></p>
                    <p><strong>Estimated BMI:</strong> ${results.bmi.toFixed(1)}</p>
                </div>
            </div>
        `;
        
        // Add detailed analysis
        resultsHTML += `
            <div style="margin-bottom: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #003366;">Analysis</h4>
                <div style="background: ${results.withinStandards ? '#d4edda' : '#f8d7da'}; padding: 15px; border-radius: 5px; border: 1px solid ${results.withinStandards ? '#c3e6cb' : '#f5c6cb'};">
                    <p style="color: ${results.withinStandards ? '#155724' : '#721c24'}; margin: 5px 0; font-weight: bold;">
                        ${results.withinStandards ? '✅ PASS' : '❌ FAIL'} - Army Body Fat Standards
                    </p>
                    <p style="color: ${results.withinStandards ? '#155724' : '#721c24'}; margin: 5px 0;">
                        Your calculated body fat percentage is ${results.bodyFatPercentage.toFixed(1)}%, which is 
                        ${results.withinStandards ? 'within' : 'above'} the maximum allowable ${results.maxAllowable}% 
                        for ${data.gender === 'm' ? 'males' : 'females'} aged ${data.age}.
                    </p>
                    ${!results.withinStandards ? `
                        <p style="color: #721c24; margin: 5px 0;">
                            <strong>Note:</strong> Soldiers who fail the circumference-based tape method may request 
                            a supplemental body fat assessment (DXA, InBody 770, or Bod Pod) if available.
                        </p>
                    ` : ''}
                </div>
            </div>
        `;
        
        // Add Army standards table
        resultsHTML += `
            <div style="margin-bottom: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #003366;">Army Body Fat Standards</h4>
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <tr style="background: #f8f9fa;">
                        <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Age Group</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">Male Max %</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">Female Max %</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">Your Category</th>
                    </tr>
                    <tr style="background: ${data.age >= 17 && data.age <= 20 ? '#fff3cd' : 'white'};">
                        <td style="border: 1px solid #dee2e6; padding: 8px;">17-20</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">20%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">30%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${data.age >= 17 && data.age <= 20 ? '👈' : ''}</td>
                    </tr>
                    <tr style="background: ${data.age >= 21 && data.age <= 27 ? '#fff3cd' : 'white'};">
                        <td style="border: 1px solid #dee2e6; padding: 8px;">21-27</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">22%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">32%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${data.age >= 21 && data.age <= 27 ? '👈' : ''}</td>
                    </tr>
                    <tr style="background: ${data.age >= 28 && data.age <= 39 ? '#fff3cd' : 'white'};">
                        <td style="border: 1px solid #dee2e6; padding: 8px;">28-39</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">24%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">34%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${data.age >= 28 && data.age <= 39 ? '👈' : ''}</td>
                    </tr>
                    <tr style="background: ${data.age >= 40 ? '#fff3cd' : 'white'};">
                        <td style="border: 1px solid #dee2e6; padding: 8px;">40 and over</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">26%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">36%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${data.age >= 40 ? '👈' : ''}</td>
                    </tr>
                </table>
            </div>
        `;
        
        // Add important notes
        resultsHTML += `
            <div style="margin-bottom: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #003366;">Important Notes</h4>
                <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; font-size: 14px;">
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>This calculation is based on the Army Body Fat Assessment (June 12, 2023)</li>
                        <li>Soldiers scoring 540+ on ACFT with 80+ points per event are exempt from body fat assessment</li>
                        <li>Take the average of at least three measurements for accuracy</li>
                        <li>Round measurements to the nearest pound (weight) or 0.5 inch (waist)</li>
                        <li>Supplemental assessments (DXA, InBody 770, Bod Pod) may be requested if available</li>
                    </ul>
                </div>
            </div>
        `;
        
        this.resultsContent.innerHTML = resultsHTML;
        this.resultSection.style.display = 'block';
        
        // Update result title
        this.resultTitle.textContent = `Body Fat: ${results.bodyFatPercentage.toFixed(1)}% - ${statusText}`;
    }
    
    showError(message) {
        if (!this.resultSection || !this.resultsContent) return;
        
        this.resultsContent.innerHTML = `
            <div style="background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;">
                <h4 style="margin: 0 0 10px 0; color: #721c24;">❌ Calculation Error</h4>
                <p style="color: #721c24; margin: 0;">${message}</p>
            </div>
        `;
        this.resultSection.style.display = 'block';
        this.resultTitle.textContent = 'Calculation Error';
    }
    
    clearForm() {
        // Reset form to default values
        document.getElementById('csex1').checked = true;
        document.getElementById('csex2').checked = false;
        document.getElementById('cage').value = '21';
        document.getElementById('cweight').value = '165';
        document.getElementById('cwaistfeet').value = '3';
        document.getElementById('cwaistinch').value = '0';
        
        // Reset input styling
        const inputs = this.form.querySelectorAll('input[type="text"]');
        inputs.forEach(input => {
            input.style.borderColor = '';
            input.style.backgroundColor = '';
        });
        
        // Hide results
        if (this.resultSection) {
            this.resultSection.style.display = 'none';
        }
    }
}

// Initialize calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.armyBodyFatCalculator = new ArmyBodyFatCalculator();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ArmyBodyFatCalculator;
}
