<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Mobile Scroll Test - Dry Calculator</title>
    <meta name="description" content="Test page for mobile result scrolling functionality.">
    <link rel="stylesheet" href="css/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="images/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/icon-16x16.png">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo">
            <a href="index.html">
                <img src="images/dry-calculator-logo.svg" width="208" height="22" alt="Dry Calculator">
            </a>
        </div>
        <div id="login">
            <!-- <PERSON><PERSON> removed -->
        </div>
    </div>
</div>
<div id="clear"></div>

<div id="contentout">
    <div id="content">
        <div id="breadcrumbs">
            <a href="index.html">home</a> / 
            <a href="mobile-scroll-test.html">mobile scroll test</a>
        </div>
        
        <h1>Mobile Result Scrolling Test</h1>
        <p>This page tests the automatic scrolling to results functionality on mobile devices.</p>

        <div id="insmdc">
            <img src="images/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use">
        </div>
        
        <!-- Test Status Display -->
        <div id="test-status" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #dee2e6;">
            <h3 style="margin: 0 0 10px 0; color: #495057;">📱 Test Status</h3>
            <div id="device-info">
                <p><strong>Device Type:</strong> <span id="device-type">Detecting...</span></p>
                <p><strong>Screen Size:</strong> <span id="screen-size">Detecting...</span></p>
                <p><strong>Touch Support:</strong> <span id="touch-support">Detecting...</span></p>
                <p><strong>Mobile Scroll Active:</strong> <span id="mobile-scroll-status">Detecting...</span></p>
            </div>
        </div>
        
        <!-- Instructions -->
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;">
            <h3 style="margin: 0 0 10px 0; color: #004085;">🧪 How to Test</h3>
            <ol style="margin: 10px 0; padding-left: 20px;">
                <li>Fill in the form below (or use default values)</li>
                <li>Scroll up so the form is at the top of your screen</li>
                <li>Tap the "Calculate" button</li>
                <li>Watch for automatic scroll to results</li>
                <li>Look for visual feedback and success indicators</li>
            </ol>
        </div>
        
        <!-- Add content to ensure scrolling is needed -->
        <div style="margin: 30px 0;">
            <h2>Calculator Form</h2>
            <p>This form simulates a typical calculator. When you submit it on a mobile device, the page should automatically scroll to show the results.</p>
        </div>
        
        <form name="calform">
            <div class="panel">
                <table id="calinputtable">
                    <tbody>
                        <tr>
                            <td>Temperature</td>
                            <td>
                                <input type="text" name="cinputtemp" id="cinputtemp" value="25" class="infull">
                                <span style="margin-left: 5px; color: rgb(136, 136, 136);">°C</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Humidity</td>
                            <td>
                                <input type="text" name="cinputhumidity" id="cinputhumidity" value="60" class="infull">
                                <span style="margin-left: 5px; color: rgb(136, 136, 136);">%</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Pressure</td>
                            <td>
                                <input type="text" name="cinputpressure" id="cinputpressure" value="101.325" class="infull">
                                <span style="margin-left: 5px; color: rgb(136, 136, 136);">kPa</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Calculation Mode</td>
                            <td>
                                <select name="ccalcmode" id="ccalcmode" class="infull">
                                    <option value="standard">Standard Calculation</option>
                                    <option value="detailed">Detailed Analysis</option>
                                    <option value="advanced">Advanced Mode</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" align="left" style="padding-top: 8px;padding-left: 70px;">
                                <input type="submit" name="x" value="Calculate">
                                <input type="button" value="Clear" onclick="clearTestForm()">
                                <input type="button" value="Manual Scroll Test" onclick="testManualScroll()">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </form>

        <!-- Add more content to ensure results are below the fold -->
        <div style="margin: 40px 0;">
            <h2>Expected Behavior</h2>
            <div style="background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb;">
                <h4 style="margin: 0 0 10px 0; color: #155724;">✅ On Mobile Devices:</h4>
                <ul style="color: #155724; margin: 10px 0; padding-left: 20px;">
                    <li>Button shows loading state during calculation</li>
                    <li>Page automatically scrolls to results section</li>
                    <li>Results section highlights with blue glow</li>
                    <li>Success indicator appears briefly</li>
                    <li>Haptic feedback (vibration) if supported</li>
                </ul>
            </div>
            
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7; margin-top: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #856404;">⚠️ On Desktop:</h4>
                <ul style="color: #856404; margin: 10px 0; padding-left: 20px;">
                    <li>Auto-scroll is disabled (not needed)</li>
                    <li>Results appear normally without scrolling</li>
                    <li>All other functionality works as expected</li>
                </ul>
            </div>
        </div>
        
        <!-- More content to push results further down -->
        <div style="margin: 40px 0;">
            <h2>Technical Details</h2>
            <p>The mobile scrolling system uses multiple detection methods:</p>
            <ul>
                <li><strong>Form Submission Hook:</strong> Detects when calculate button is pressed</li>
                <li><strong>MutationObserver:</strong> Monitors DOM changes for result appearance</li>
                <li><strong>Periodic Check:</strong> Fallback system that checks every 2 seconds</li>
                <li><strong>Manual Trigger:</strong> Global function for manual activation</li>
            </ul>
            
            <h3>Debug Information</h3>
            <div id="debug-log" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6;">
                <div>Debug log will appear here...</div>
            </div>
        </div>

        <!-- Results section (this should be scrolled to) -->
        <div id="result-section" style="display: none; margin-top: 30px; padding: 20px; background: #f0f8ff; border: 2px solid #007bff; border-radius: 8px;">
            <h3>🎯 Calculation Results</h3>
            <div id="results-content">
                <!-- Results will be populated here -->
            </div>
        </div>

        <br>
        <fieldset>
            <legend>Related Tests</legend>
            <a href="mobile-calculation-test.html">Mobile Calculation Test</a> | 
            <a href="temperature-calculator.html">Temperature Calculator</a> | 
            <a href="mobile-test.html">Mobile Layout Test</a>
        </fieldset>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688"
                 crossorigin="anonymous"></script>
            <!-- zishiying -->
            <ins class="adsbygoogle"
                 style="display:block"
                 data-ad-client="ca-pub-2205593928173688"
                 data-ad-slot="8971293106"
                 data-ad-format="auto"
                 data-full-width-responsive="true"></ins>
            <script>
                 (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
        
        <form name="calcSearchForm" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td>
                            <input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators...">
                        </td>
                        <td>
                            <span id="bluebtn">Search</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="other-calculators.html">Other Calculators</a>
            </div>
            <div id="occontent">
                <a href="temperature-calculator.html">Temperature</a>
                <a href="pressure-calculator.html">Pressure</a>
                <a href="density-calculator.html">Density</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="about-us.html">about us</a> | 
            <a href="sitemap.html">sitemap</a> | 
            <a href="terms.html">terms of use</a> | 
            <a href="privacy.html">privacy policy</a> 
            &nbsp; © 2025 <a href="index.html">drycalculator.net</a>
        </div>
    </div>
</div>

<script src="js/simple-mobile-scroll.js"></script>
<script>
// Test calculator functionality
class MobileScrollTestCalculator {
    constructor() {
        this.form = document.querySelector('form[name="calform"]');
        this.resultSection = document.getElementById('result-section');
        this.resultsContent = document.getElementById('results-content');
        this.debugLog = document.getElementById('debug-log');
        
        this.initializeForm();
        this.updateDeviceInfo();
        this.setupDebugLogging();
    }
    
    initializeForm() {
        if (!this.form) return;
        
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.log('Form submitted');
            this.calculateTest();
        });
    }
    
    updateDeviceInfo() {
        const isMobile = window.innerWidth <= 768;
        const hasTouch = 'ontouchstart' in window;
        const mobileScrollActive = window.simpleMobileScroll && window.simpleMobileScroll.isMobile;
        
        document.getElementById('device-type').textContent = isMobile ? 'Mobile/Tablet' : 'Desktop';
        document.getElementById('screen-size').textContent = `${window.innerWidth} x ${window.innerHeight}`;
        document.getElementById('touch-support').textContent = hasTouch ? 'Yes' : 'No';
        document.getElementById('mobile-scroll-status').textContent = mobileScrollActive ? 'Active' : 'Inactive';
        
        // Update on resize
        window.addEventListener('resize', () => {
            setTimeout(() => this.updateDeviceInfo(), 100);
        });
    }
    
    setupDebugLogging() {
        // Override console.log to capture debug messages
        const originalLog = console.log;
        console.log = (...args) => {
            originalLog.apply(console, args);
            this.log(args.join(' '));
        };
    }
    
    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        this.debugLog.appendChild(logEntry);
        this.debugLog.scrollTop = this.debugLog.scrollHeight;
        
        // Keep only last 20 entries
        while (this.debugLog.children.length > 20) {
            this.debugLog.removeChild(this.debugLog.firstChild);
        }
    }
    
    calculateTest() {
        const temp = parseFloat(document.getElementById('cinputtemp').value) || 25;
        const humidity = parseFloat(document.getElementById('cinputhumidity').value) || 60;
        const pressure = parseFloat(document.getElementById('cinputpressure').value) || 101.325;
        const mode = document.getElementById('ccalcmode').value;
        
        this.log(`Calculating: T=${temp}°C, RH=${humidity}%, P=${pressure}kPa, Mode=${mode}`);
        
        // Simulate calculation delay
        setTimeout(() => {
            this.showResults({ temp, humidity, pressure, mode });
        }, 500);
    }
    
    showResults(data) {
        if (!this.resultSection || !this.resultsContent) return;
        
        this.log('Showing results...');
        
        // Calculate some values
        const dewPoint = data.temp - ((100 - data.humidity) / 5);
        const absoluteHumidity = (6.112 * Math.exp((17.67 * data.temp) / (data.temp + 243.5)) * data.humidity * 2.1674) / (273.15 + data.temp);
        
        let resultsHTML = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #003366;">Input Values</h4>
                    <p><strong>Temperature:</strong> ${data.temp}°C</p>
                    <p><strong>Humidity:</strong> ${data.humidity}%</p>
                    <p><strong>Pressure:</strong> ${data.pressure} kPa</p>
                    <p><strong>Mode:</strong> ${data.mode}</p>
                </div>
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #003366;">Calculated Results</h4>
                    <p><strong>Dew Point:</strong> ${dewPoint.toFixed(2)}°C</p>
                    <p><strong>Absolute Humidity:</strong> ${absoluteHumidity.toFixed(2)} g/m³</p>
                    <p><strong>Calculation Time:</strong> ${new Date().toLocaleTimeString()}</p>
                </div>
            </div>
        `;
        
        // Add mobile scroll test status
        const isMobile = window.innerWidth <= 768;
        const scrollStatus = window.simpleMobileScroll ? 'Active' : 'Not Loaded';
        
        resultsHTML += `
            <div style="margin-bottom: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #003366;">📱 Mobile Scroll Test Status</h4>
                <div style="background: ${isMobile ? '#d4edda' : '#fff3cd'}; padding: 10px; border-radius: 5px; border: 1px solid ${isMobile ? '#c3e6cb' : '#ffeaa7'};">
                    <p style="color: ${isMobile ? '#155724' : '#856404'}; margin: 5px 0;">
                        <strong>Device:</strong> ${isMobile ? 'Mobile (scroll should activate)' : 'Desktop (scroll disabled)'}
                    </p>
                    <p style="color: ${isMobile ? '#155724' : '#856404'}; margin: 5px 0;">
                        <strong>Scroll System:</strong> ${scrollStatus}
                    </p>
                    <p style="color: ${isMobile ? '#155724' : '#856404'}; margin: 5px 0;">
                        <strong>Current Position:</strong> ${window.scrollY}px
                    </p>
                    <p style="color: ${isMobile ? '#155724' : '#856404'}; margin: 5px 0;">
                        <strong>Result Position:</strong> ${this.resultSection.offsetTop}px
                    </p>
                </div>
            </div>
        `;
        
        if (data.mode === 'detailed' || data.mode === 'advanced') {
            resultsHTML += `
                <div style="margin-bottom: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #003366;">Detailed Analysis</h4>
                    <div style="background: #f8f8f8; padding: 10px; border-radius: 5px;">
                        <p><strong>Saturation Pressure:</strong> ${(6.112 * Math.exp((17.67 * data.temp) / (data.temp + 243.5))).toFixed(2)} hPa</p>
                        <p><strong>Vapor Pressure:</strong> ${(6.112 * Math.exp((17.67 * data.temp) / (data.temp + 243.5)) * data.humidity / 100).toFixed(2)} hPa</p>
                        <p><strong>Mixing Ratio:</strong> ${(0.622 * (6.112 * Math.exp((17.67 * data.temp) / (data.temp + 243.5)) * data.humidity / 100) / (data.pressure - (6.112 * Math.exp((17.67 * data.temp) / (data.temp + 243.5)) * data.humidity / 100))).toFixed(4)} kg/kg</p>
                    </div>
                </div>
            `;
        }
        
        this.resultsContent.innerHTML = resultsHTML;
        this.resultSection.style.display = 'block';
        
        this.log('Results displayed, waiting for scroll...');
    }
    
    hideResults() {
        if (this.resultSection) {
            this.resultSection.style.display = 'none';
            this.log('Results hidden');
        }
    }
}

// Global functions for testing
function clearTestForm() {
    document.getElementById('cinputtemp').value = '25';
    document.getElementById('cinputhumidity').value = '60';
    document.getElementById('cinputpressure').value = '101.325';
    document.getElementById('ccalcmode').value = 'standard';
    
    const resultSection = document.getElementById('result-section');
    if (resultSection) {
        resultSection.style.display = 'none';
    }
    
    console.log('Form cleared');
}

function testManualScroll() {
    console.log('Manual scroll test triggered');
    
    // Show results first
    const resultSection = document.getElementById('result-section');
    const resultsContent = document.getElementById('results-content');
    
    if (resultSection && resultsContent) {
        resultsContent.innerHTML = `
            <div style="background: #e7f3ff; padding: 15px; border-radius: 5px;">
                <h4 style="margin: 0 0 10px 0; color: #004085;">🧪 Manual Scroll Test</h4>
                <p style="color: #004085; margin: 5px 0;">This is a manual test of the scroll functionality.</p>
                <p style="color: #004085; margin: 5px 0;"><strong>Triggered at:</strong> ${new Date().toLocaleTimeString()}</p>
                <p style="color: #004085; margin: 5px 0;"><strong>Scroll Position:</strong> ${window.scrollY}px</p>
            </div>
        `;
        resultSection.style.display = 'block';
        
        // Trigger manual scroll
        if (window.scrollToMobileResults) {
            window.scrollToMobileResults();
        } else if (window.simpleMobileScroll) {
            window.simpleMobileScroll.scrollToResults();
        } else {
            console.log('No mobile scroll system found');
        }
    }
}

// Initialize test calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MobileScrollTestCalculator();
});
</script>
</body>
</html>
