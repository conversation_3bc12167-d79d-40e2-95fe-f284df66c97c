<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Homepage Mobile Test - Dry Calculator</title>
    <meta name="description" content="Test page for homepage mobile layout fixes.">
    <link rel="stylesheet" href="css/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="images/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/icon-16x16.png">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo">
            <a href="index.html">
                <img src="images/dry-calculator-logo.svg" width="208" height="22" alt="Dry Calculator">
            </a>
        </div>
        <div id="login">
            <!-- <PERSON>gin removed -->
        </div>
    </div>
</div>
<div id="clear"></div>

<div id="homecaldiv">
    <div id="contentout">
        <table align="center">
            <tbody>
                <tr>
                    <td>
                        <!-- Calculator Section -->
                        <div id="sciout">
                            <table>
                                <tbody>
                                    <tr>
                                        <td colspan="5" id="sciInPut">0</td>
                                    </tr>
                                    <tr>
                                        <td colspan="5" id="sciOutPut">0</td>
                                    </tr>
                                </tbody>
                            </table>
                            <div id="homefunbtn">
                                <div>
                                    <span class="scifunc" onclick="scical('AC')">AC</span>
                                    <span class="scifunc" onclick="scical('C')">C</span>
                                    <span class="scifunc" onclick="scical('back')">&larr;</span>
                                    <span class="sciop" onclick="scical('/')">/</span>
                                    <span class="sciop" onclick="scical('*')">×</span>
                                </div>
                                <div>
                                    <span class="scinm" onclick="scical('7')">7</span>
                                    <span class="scinm" onclick="scical('8')">8</span>
                                    <span class="scinm" onclick="scical('9')">9</span>
                                    <span class="sciop" onclick="scical('-')">-</span>
                                    <span class="scifunc" onclick="scical('sqrt')">√</span>
                                </div>
                                <div>
                                    <span class="scinm" onclick="scical('4')">4</span>
                                    <span class="scinm" onclick="scical('5')">5</span>
                                    <span class="scinm" onclick="scical('6')">6</span>
                                    <span class="sciop" onclick="scical('+')">+</span>
                                    <span class="scifunc" onclick="scical('pow')">x²</span>
                                </div>
                                <div>
                                    <span class="scinm" onclick="scical('1')">1</span>
                                    <span class="scinm" onclick="scical('2')">2</span>
                                    <span class="scinm" onclick="scical('3')">3</span>
                                    <span class="scieq" onclick="scical('=')" rowspan="2">=</span>
                                    <span class="scifunc" onclick="scical('1/x')">1/x</span>
                                </div>
                                <div>
                                    <span class="scinm" onclick="scical('0')" colspan="2">0</span>
                                    <span class="scinm" onclick="scical('.')">.</span>
                                    <span class="scifunc" onclick="scical('%')">%</span>
                                </div>
                                <div class="scird">
                                    <label><input type="radio" name="angleUnit" value="deg" checked> Deg</label>
                                    <label><input type="radio" name="angleUnit" value="rad"> Rad</label>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- Search Section -->
                        <div id="homesch">
                            <h1>Dry Calculator</h1>
                            <p>Professional moisture, humidity, and drying calculators for engineering and scientific applications.</p>
                            <table>
                                <tbody>
                                    <tr>
                                        <td>
                                            <input type="text" name="calcSearchTerm" id="calcSearchTerm" placeholder="Search calculators..." class="inlongest">
                                        </td>
                                        <td>
                                            <span id="bluebtn" onclick="searchCalculators()">Search</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div id="calcSearchOut"></div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Calculator Categories Section -->
<div id="contentout">
    <div id="content">
        <h2>Calculator Categories</h2>
        <div id="homelistwrap">
            <div class="homelisttile">
                <div class="hicon">
                    <img src="images/moisture-icon.svg" alt="Moisture Calculators">
                </div>
                <div class="hh">
                    <h3><a href="moisture-calculators.html">Moisture Calculators</a></h3>
                </div>
                <ul class="hl">
                    <li><a href="moisture-content-calculator.html">Moisture Content Calculator</a></li>
                    <li><a href="dry-basis-calculator.html">Dry Basis Calculator</a></li>
                    <li><a href="wet-basis-calculator.html">Wet Basis Calculator</a></li>
                    <li><a href="wood-moisture-calculator.html">Wood Moisture Calculator</a></li>
                </ul>
            </div>
            
            <div class="homelisttile">
                <div class="hicon">
                    <img src="images/humidity-icon.svg" alt="Humidity Calculators">
                </div>
                <div class="hh">
                    <h3><a href="humidity-calculators.html">Humidity Calculators</a></h3>
                </div>
                <ul class="hl">
                    <li><a href="relative-humidity-calculator.html">Relative Humidity Calculator</a></li>
                    <li><a href="absolute-humidity-calculator.html">Absolute Humidity Calculator</a></li>
                    <li><a href="dew-point-calculator.html">Dew Point Calculator</a></li>
                    <li><a href="psychrometric-calculator.html">Psychrometric Calculator</a></li>
                </ul>
            </div>
            
            <div class="homelisttile">
                <div class="hicon">
                    <img src="images/drying-icon.svg" alt="Drying Calculators">
                </div>
                <div class="hh">
                    <h3><a href="drying-calculators.html">Drying Calculators</a></h3>
                </div>
                <ul class="hl">
                    <li><a href="drying-time-calculator.html">Drying Time Calculator</a></li>
                    <li><a href="evaporation-rate-calculator.html">Evaporation Rate Calculator</a></li>
                    <li><a href="kiln-drying-calculator.html">Kiln Drying Calculator</a></li>
                    <li><a href="heat-requirement-calculator.html">Heat Requirement Calculator</a></li>
                </ul>
            </div>
            
            <div class="homelisttile">
                <div class="hicon">
                    <img src="images/tools-icon.svg" alt="Other Calculators">
                </div>
                <div class="hh">
                    <h3><a href="other-calculators.html">Other Calculators</a></h3>
                </div>
                <ul class="hl">
                    <li><a href="temperature-calculator.html">Temperature Calculator</a></li>
                    <li><a href="pressure-calculator.html">Pressure Calculator</a></li>
                    <li><a href="density-calculator.html">Density Calculator</a></li>
                    <li><a href="volume-calculator.html">Volume Calculator</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="about-us.html">about us</a> | 
            <a href="sitemap.html">sitemap</a> | 
            <a href="terms.html">terms of use</a> | 
            <a href="privacy.html">privacy policy</a> 
            &nbsp; © 2025 <a href="index.html">drycalculator.net</a>
        </div>
    </div>
</div>

<script>
// Simple calculator functionality for testing
let currentInput = '0';
let operator = '';
let previousInput = '';

function scical(value) {
    const inputDisplay = document.getElementById('sciInPut');
    const outputDisplay = document.getElementById('sciOutPut');
    
    if (value === 'AC') {
        currentInput = '0';
        operator = '';
        previousInput = '';
        inputDisplay.textContent = '0';
        outputDisplay.textContent = '0';
    } else if (value === 'C') {
        currentInput = '0';
        inputDisplay.textContent = currentInput;
    } else if (value === 'back') {
        if (currentInput.length > 1) {
            currentInput = currentInput.slice(0, -1);
        } else {
            currentInput = '0';
        }
        inputDisplay.textContent = currentInput;
    } else if (['+', '-', '*', '/'].includes(value)) {
        if (previousInput && operator && currentInput !== '0') {
            calculate();
        }
        operator = value;
        previousInput = currentInput;
        currentInput = '0';
        inputDisplay.textContent = previousInput + ' ' + operator;
    } else if (value === '=') {
        if (previousInput && operator) {
            calculate();
        }
    } else if (value === '.') {
        if (!currentInput.includes('.')) {
            currentInput += '.';
            inputDisplay.textContent = currentInput;
        }
    } else if (!isNaN(value)) {
        if (currentInput === '0') {
            currentInput = value;
        } else {
            currentInput += value;
        }
        inputDisplay.textContent = currentInput;
    }
}

function calculate() {
    const inputDisplay = document.getElementById('sciInPut');
    const outputDisplay = document.getElementById('sciOutPut');
    
    let result;
    const prev = parseFloat(previousInput);
    const current = parseFloat(currentInput);
    
    switch (operator) {
        case '+':
            result = prev + current;
            break;
        case '-':
            result = prev - current;
            break;
        case '*':
            result = prev * current;
            break;
        case '/':
            result = current !== 0 ? prev / current : 'Error';
            break;
        default:
            return;
    }
    
    currentInput = result.toString();
    operator = '';
    previousInput = '';
    
    inputDisplay.textContent = currentInput;
    outputDisplay.textContent = currentInput;
}

function searchCalculators() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim()) {
        searchOut.innerHTML = `<p>Search results for: "${searchTerm}"</p><p>This is a test - search functionality would be implemented here.</p>`;
    } else {
        searchOut.innerHTML = '';
    }
}

// Test mobile layout on load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Homepage mobile test loaded');
    console.log('Screen width:', window.innerWidth);
    console.log('Screen height:', window.innerHeight);
    
    // Test responsive behavior
    window.addEventListener('resize', function() {
        console.log('Window resized to:', window.innerWidth, 'x', window.innerHeight);
    });
});
</script>

<style>
/* Additional test styles */
.test-info {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 1000;
}

@media (max-width: 600px) {
    .test-info {
        position: static;
        margin: 10px;
        text-align: center;
    }
}
</style>

<div class="test-info">
    <div>Screen: <span id="screen-size"></span></div>
    <div>Layout: <span id="layout-mode"></span></div>
</div>

<script>
function updateTestInfo() {
    const screenSize = document.getElementById('screen-size');
    const layoutMode = document.getElementById('layout-mode');
    
    screenSize.textContent = window.innerWidth + 'x' + window.innerHeight;
    
    if (window.innerWidth <= 480) {
        layoutMode.textContent = 'Small Mobile';
    } else if (window.innerWidth <= 600) {
        layoutMode.textContent = 'Mobile';
    } else if (window.innerWidth <= 768) {
        layoutMode.textContent = 'Large Mobile';
    } else if (window.innerWidth <= 1020) {
        layoutMode.textContent = 'Tablet';
    } else {
        layoutMode.textContent = 'Desktop';
    }
}

updateTestInfo();
window.addEventListener('resize', updateTestInfo);
</script>
</body>
</html>
